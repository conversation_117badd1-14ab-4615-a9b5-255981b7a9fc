"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>alog, DialogContent, DialogDescription, DialogFooter, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loader2 } from "lucide-react";

interface Part {
  id: string;
  title: string;
}

interface Lesson {
  title: string;
  description: string;
  partId: string;
  content: string;
  order: number;
  level: "beginner" | "intermediate" | "advanced";
  duration: number;
}

interface AddLessonDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (lesson: Lesson) => void;
  parts: Part[];
  initialData?: Lesson;
}

export function AddLessonDialog({ open, onOpenChange, onSubmit, parts, initialData }: AddLessonDialogProps) {
  const [activeTab, setActiveTab] = useState("basic");
  const [selectedPartId, setSelectedPartId] = useState<string>("");
  const [formData, setFormData] = useState<Lesson>(
    initialData || {
      title: "",
      description: "",
      partId: "",
      content: "",
      order: 1,
      level: "beginner",
      duration: 20,
    }
  );

  // إعادة تعيين الحالة عند فتح/إغلاق الحوار
  useEffect(() => {
    if (open) {
      // إعادة تعيين selectedPartId عند فتح الحوار
      setSelectedPartId("");
      setActiveTab("basic");

      // إذا كان هناك بيانات أولية، قم بتعيين الجزء المناسب
      if (initialData?.partId) {
        setSelectedPartId(initialData.partId);
      }
    }
  }, [open, initialData]);

  // لم تعد هناك حاجة لفلترة الوحدات - نعمل مع الأجزاء مباشرة

  const [loading, setLoading] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: name === "order" || name === "duration" ? parseInt(value) || 0 : value,
    });
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.title || !formData.partId) {
      return;
    }

    setLoading(true);

    try {
      // محاكاة تأخير الشبكة
      await new Promise((resolve) => setTimeout(resolve, 500));

      onSubmit(formData);

      // إعادة تعيين النموذج إذا كان إضافة جديدة
      if (!initialData) {
        setFormData({
          title: "",
          description: "",
          partId: "",
          content: "",
          order: 1,
          level: "beginner",
          duration: 20,
        });
        setSelectedPartId("");
        setActiveTab("basic");
      }
    } catch (error) {
      console.error("Error submitting lesson:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{initialData ? "تعديل الدرس" : "إضافة درس جديد"}</DialogTitle>
          <DialogDescription>
            {initialData
              ? "قم بتعديل معلومات الدرس التعليمي"
              : "أدخل معلومات الدرس التعليمي الجديد"}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="mb-4 grid w-full grid-cols-2">
              <TabsTrigger value="basic">المعلومات الأساسية</TabsTrigger>
              <TabsTrigger value="content">المحتوى</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="title">عنوان الدرس</Label>
                <Input
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleChange}
                  placeholder="أدخل عنوان الدرس"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">وصف الدرس</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  placeholder="أدخل وصفاً مختصراً للدرس"
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="partId">الجزء *</Label>
                <Select
                  value={formData.partId}
                  onValueChange={(value) => handleSelectChange("partId", value)}
                >
                  <SelectTrigger id="partId">
                    <SelectValue placeholder="اختر الجزء" />
                  </SelectTrigger>
                  <SelectContent>
                    {parts.map((part) => (
                      <SelectItem key={part.id} value={part.id}>
                        {part.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="order">الترتيب</Label>
                  <Input
                    id="order"
                    name="order"
                    type="number"
                    min={1}
                    value={formData.order}
                    onChange={handleChange}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="level">المستوى</Label>
                  <Select
                    value={formData.level}
                    onValueChange={(value) => handleSelectChange("level", value as "beginner" | "intermediate" | "advanced")}
                  >
                    <SelectTrigger id="level">
                      <SelectValue placeholder="اختر المستوى" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="beginner">مبتدئ</SelectItem>
                      <SelectItem value="intermediate">متوسط</SelectItem>
                      <SelectItem value="advanced">متقدم</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="duration">المدة (دقائق)</Label>
                  <Input
                    id="duration"
                    name="duration"
                    type="number"
                    min={1}
                    value={formData.duration}
                    onChange={handleChange}
                  />
                </div>
              </div>

              <div className="flex justify-end">
                <Button type="button" onClick={() => setActiveTab("content")}>
                  التالي: المحتوى
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="content" className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="content">محتوى الدرس</Label>
                <Textarea
                  id="content"
                  name="content"
                  value={formData.content}
                  onChange={handleChange}
                  placeholder="أدخل محتوى الدرس"
                  rows={10}
                />
                <p className="text-xs text-muted-foreground">
                  يمكنك استخدام Markdown لتنسيق المحتوى
                </p>
              </div>

              <div className="flex justify-between">
                <Button type="button" variant="outline" onClick={() => setActiveTab("basic")}>
                  السابق: المعلومات الأساسية
                </Button>

                <Button type="submit" disabled={loading || !formData.title || !formData.partId}>
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      جاري الحفظ...
                    </>
                  ) : initialData ? (
                    "حفظ التغييرات"
                  ) : (
                    "إضافة الدرس"
                  )}
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </form>
      </DialogContent>
    </Dialog>
  );
}
