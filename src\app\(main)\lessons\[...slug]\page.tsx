"use client";

import { motion } from "framer-motion";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useDynamicBackground } from "@/hooks/use-dynamic-background";
import { LessonsGrid } from "@/components/lessons/lessons-grid";
import { ComponentsGrid } from "@/components/lessons/components-grid";
import { FullScreenComponentViewer } from "@/components/lessons/fullscreen-component-viewer";
import { Loading } from "@/components/dashboard/loading";
import { Button } from "@/components/ui/button";
import {
  ArrowLeft,
  Home,
  BookOpen
} from "lucide-react";
import Link from "next/link";

interface BreadcrumbItem {
  label: string;
  href: string;
  icon?: any;
}

interface TextPart {
  text: string;
  color: string;
  fontWeight?: "light" | "normal" | "bold";
}

interface LessonItem {
  id: string;
  type: string;
  order: number;
  parts: TextPart[];
  backgroundColor?: string;
  audioUrl?: string;
  spellingAudioUrl?: string;
}

export default function DynamicLessonsPage() {
  const params = useParams();
  const router = useRouter();
  const { setColorFromContent } = useDynamicBackground();

  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [breadcrumbs, setBreadcrumbs] = useState<BreadcrumbItem[]>([]);
  const [pageType, setPageType] = useState<'lessons' | 'components'>('lessons');
  
  // Fullscreen component viewer state
  const [isViewerOpen, setIsViewerOpen] = useState(false);
  const [currentItemIndex, setCurrentItemIndex] = useState(0);

  const slug = Array.isArray(params?.slug) ? params.slug : [];

  useEffect(() => {
    parseSlugAndFetchData();
  }, [slug]);

  const parseSlugAndFetchData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Parse the slug to determine what to fetch
      if (slug.length === 0) {
        // /lessons - show parts (handled by parent page)
        router.push('/lessons');
        return;
      }

      const newBreadcrumbs: BreadcrumbItem[] = [
        { label: "الرئيسية", href: "/", icon: Home },
        { label: "الأجزاء", href: "/lessons", icon: BookOpen },
      ];

      if (slug[0] === 'part' && slug[1]) {
        // /lessons/part/[partId] - show lessons for this part
        const partId = slug[1];

        if (slug.length === 2) {
          // Show lessons for this part
          const response = await fetch(`/api/lessons?partId=${partId}`);
          const lessonsData = await response.json();

          // Get part info
          const partResponse = await fetch(`/api/parts/${partId}`);
          const partData = await partResponse.json();

          setData({ lessons: lessonsData.lessons, part: partData.part });
          setPageType('lessons');
          setColorFromContent(partData.part?.title || 'دروس الجزء');

          newBreadcrumbs.push({
            label: partData.part?.title || `الجزء ${partId}`,
            href: `/lessons/part/${partId}`,
          });

        } else if (slug[2] === 'lesson' && slug[3]) {
          // /lessons/part/[partId]/lesson/[lessonId] - show components
          const lessonId = slug[3];

          // Get lesson components
          const response = await fetch(`/api/lesson-items?lessonId=${lessonId}`);
          const componentsData = await response.json();

          // Get lesson and part info
          const lessonResponse = await fetch(`/api/lessons/${lessonId}`);
          const lessonData = await lessonResponse.json();

          // Sort components by order
          const sortedComponents = (componentsData.lessonItems || []).sort((a: LessonItem, b: LessonItem) => a.order - b.order);

          setData({ components: sortedComponents, lesson: lessonData.lesson });
          setPageType('components');
          setColorFromContent(lessonData.lesson?.title || 'مكونات الدرس');

          newBreadcrumbs.push(
            {
              label: lessonData.lesson?.part?.title || 'الجزء',
              href: `/lessons/part/${lessonData.lesson?.partId}`,
            },
            {
              label: lessonData.lesson?.title || `الدرس ${lessonId}`,
              href: `/lessons/part/${lessonData.lesson?.partId}/lesson/${lessonId}`,
            }
          );
        }
      }

      setBreadcrumbs(newBreadcrumbs);

    } catch (err) {
      console.error('Error fetching data:', err);
      setError('حدث خطأ أثناء تحميل البيانات');
    } finally {
      setLoading(false);
    }
  };

  // Open fullscreen component viewer
  const openViewer = (index: number) => {
    setCurrentItemIndex(index);
    setIsViewerOpen(true);
  };

  // Close component viewer
  const closeViewer = () => {
    setIsViewerOpen(false);
  };

  // Go to next component
  const goToNextItem = () => {
    if (data && data.components && currentItemIndex < data.components.length - 1) {
      setCurrentItemIndex(currentItemIndex + 1);
    }
  };

  // Go to previous component
  const goToPreviousItem = () => {
    if (currentItemIndex > 0) {
      setCurrentItemIndex(currentItemIndex - 1);
    }
  };

  if (loading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <Loading text="جاري التحميل..." size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-[80vh] flex items-center justify-center">
        <div className="text-center max-w-md">
          <div className="w-16 h-16 rounded-full bg-destructive/10 flex items-center justify-center mx-auto mb-4">
            <span className="text-2xl">⚠️</span>
          </div>
          <h3 className="text-xl font-bold mb-4">حدث خطأ</h3>
          <p className="text-muted-foreground mb-6">{error}</p>
          <Button onClick={() => router.back()} variant="outline">
            <ArrowLeft className="w-4 h-4 mr-2" />
            العودة
          </Button>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      className="py-8 md:py-16"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
    >
      <div className="container mx-auto px-4">
        {/* Breadcrumb */}
        <motion.nav
          className="flex items-center gap-2 mb-8 text-sm"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
        >
          {breadcrumbs.map((item, index) => (
            <div key={item.href} className="flex items-center gap-2">
              {index > 0 && <span className="text-muted-foreground">/</span>}
              <Link
                href={item.href}
                className={`flex items-center gap-1 hover:text-primary transition-colors ${
                  index === breadcrumbs.length - 1
                    ? 'text-primary font-medium'
                    : 'text-muted-foreground'
                }`}
              >
                {item.icon && <item.icon className="w-4 h-4" />}
                {item.label}
              </Link>
            </div>
          ))}
        </motion.nav>

        {/* Content */}
        {pageType === 'lessons' && data && 'lessons' in data && (
          <LessonsGrid lessons={data.lessons} />
        )}

        {pageType === 'components' && data && 'components' in data && (
          <ComponentsGrid 
            items={data.components} 
            onItemClick={openViewer}
            loading={loading}
          />
        )}
      </div>

      {/* Fullscreen Component Viewer */}
      {pageType === 'components' && data && data.components && data.components.length > 0 && (
        <FullScreenComponentViewer
          items={data.components}
          currentIndex={currentItemIndex}
          isOpen={isViewerOpen}
          onClose={closeViewer}
          onNext={goToNextItem}
          onPrevious={goToPreviousItem}
        />
      )}
    </motion.div>
  );
}
