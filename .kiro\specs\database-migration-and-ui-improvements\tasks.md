# خطة تنفيذ ترحيل قاعدة البيانات وتحسين واجهة المستخدم

- [x] 1. إنشاء سكريبت النسخ الاحتياطي وتصفية البيانات

  - إنشاء سكريبت لتصدير البيانات الحالية من PostgreSQL إلى ملف JSON
  - تنفيذ آلية تصفية لإزالة جميع البيانات المتعلقة بالوحدات (Units) فقط مع الاحتفاظ بالأجزاء (Parts)
  - إضافة التحقق من صحة البيانات المصدرة
  - _المتطلبات: 1.1, 1.2, 1.3_

- [ ] 2. تحديث مخطط قاعدة البيانات وإعداد MySQL

  - تحديث ملف schema.prisma لإزالة نموذج Unit فقط مع الاحتفاظ بنموذج Part
  - تحديث نموذج Lesson لإزالة المرجع إلى unitId وإضافة مرجع مباشر إلى partId
  - تحسين نموذج LessonItem لدعم المسافات المتعددة في حقل parts
  - تغيير مزود قاعدة البيانات من PostgreSQL إلى MySQL
  - _المتطلبات: 2.1, 2.2, 2.3, 2.4_

- [x] 3. إنشاء سكريبت استيراد البيانات إلى MySQL

  - تطوير سكريبت لقراءة ملف JSON المصفى
  - تنفيذ آلية استيراد البيانات إلى قاعدة بيانات MySQL الجديدة
  - إضافة معالجة الأخطاء والتحقق من صحة البيانات أثناء الاستيراد
  - _المتطلبات: 1.4_

- [x] 4. إزالة API الخاص بالوحدات فقط

  - حذف ملف src/app/api/units/route.ts
  - الاحتفاظ بملف src/app/api/parts/route.ts مع تحديثات طفيفة
  - تحديث أي مراجع لـ API الوحدات في الكود
  - _المتطلبات: 3.3_

- [x] 5. تحديث API الدروس لإزالة مراجع الوحدات مع الاحتفاظ بالأجزاء

  - تعديل src/app/api/lessons/route.ts لإزالة التجميع بالوحدات مع الاحتفاظ بالتجميع بالأجزاء
  - تحديث استعلامات قاعدة البيانات لجلب الدروس مجمعة بالأجزاء مباشرة
  - إزالة حقول unitId وإضافة حقل partId في عمليات إنشاء وتحديث الدروس
  - _المتطلبات: 3.1, 3.2_

- [x] 6. تحسين API مكونات الدروس لدعم المسافات المتعددة

  - تحديث src/app/api/lesson-items/route.ts لدعم المسافات المتعددة في النصوص
  - تحسين التحقق من صحة البيانات لحقل parts
  - إضافة معالجة خاصة للنصوص التي تحتوي على مسافات متعددة
  - _المتطلبات: 2.3, 6.2_

- [x] 7. تحديث مكون عرض الأجزاء والدروس

  - الاحتفاظ بعرض الأجزاء في src/app/(main)/lessons/page.tsx
  - إنشاء مكون LessonsGrid لعرض الدروس داخل كل جزء
  - إزالة مراجع الوحدات فقط مع الاحتفاظ بالأجزاء
  - تنفيذ عرض مبسط للدروس مع بطاقات محسنة
  - _المتطلبات: 3.1_

- [ ] 8. تطوير عارض محتوى الدرس المحسن (نمط معرض الصور)

  - إنشاء مكون ComponentsGrid لعرض مكونات الدرس في شبكة مثل الألبومات
  - تطوير مكون FullScreenComponentViewer لعرض المكون بملء الشاشة كاملة
  - تنفيذ أدوات التنقل بالأسهم (يمين/يسار) للانتقال بين المكونات
  - إضافة دعم التنقل بالسحب (swipe) لليمين واليسار
  - إضافة زر إغلاق للعودة إلى شبكة المكونات
  - إضافة تحميل مسبق للمكونات لتحسين الأداء
  - _المتطلبات: 4.1, 4.2, 4.3, 4.4, 4.5_

- [x] 9. تحسين مكون عرض المكونات التعليمية

  - تحديث عرض المكونات لإظهار البطاقة فقط دون عناصر تحكم إضافية
  - تحسين معالجة النصوص التي تحتوي على مسافات متعددة
  - تحسين تنسيق وعرض المكونات في الشبكة
  - _المتطلبات: 6.1, 6.2, 6.3_

- [x] 10. إنشاء مكون تبديل السمة المبسط

  - تطوير مكون SimpleThemeToggle يتبدل مباشرة بين الوضع النهاري والليلي
  - إزالة خيار "النظام" وقائمة الخيارات المنسدلة
  - تنفيذ تبديل مباشر عند النقر على الزر
  - _المتطلبات: 5.3_

- [ ] 11. تطوير مكون الشعار التكيفي







  - إنشاء مكون AdaptiveLogo يتغير حسب السمة الحالية
  - إضافة شعار منفصل للوضع الليلي (أبيض/فاتح)
  - تحديث جميع استخدامات الشعار في التطبيق
  - _المتطلبات: 5.2_

- [x] 12. تحسين ألوان وتباين الوضع الليلي



  - مراجعة وتحديث متغيرات CSS للوضع الليلي
  - التأكد من وضوح جميع النصوص والأيقونات في الوضع الليلي
  - تحسين تباين الألوان لسهولة القراءة



  - اختبار جميع المكونات في الوضع الليلي
  - _المتطلبات: 5.1, 5.4_

- [ ] 13. تحديث صفحات لوحة التحكم لإزالة مراجع الوحدات مع الاحتفاظ بالأجزاء

  - تحديث src/app/dashboard/lessons/page.tsx لإزالة عرض الوحدات مع الاحتفاظ بعرض الأجزاء
  - تحسين عرض الدروس في لوحة التحكم مجمعة بالأجزاء
  - تحديث إحصائيات التقدم لتعكس البنية الجديدة (جزء → دروس → مكونات)
  - _المتطلبات: 3.1_

- [ ] 14. تحديث مكونات التنقل والقوائم

  - إزالة روابط الوحدات والأجزاء من القوائم
  - تحديث مسارات التنقل لتعكس البنية الجديدة
  - تحسين تجربة التنقل العامة
  - _المتطلبات: 3.3_

- [ ] 15. تحسين الأداء وتحميل البيانات

  - تنفيذ تحميل مسبق للمكونات في عارض الدرس
  - تحسين استعلامات قاعدة البيانات للأداء الأمثل
  - إضافة مؤشرات تحميل محسنة
  - _المتطلبات: 7.1, 7.2, 7.3_

- [ ] 16. إنشاء اختبارات شاملة للنظام المحدث

  - كتابة اختبارات للسكريبتات الجديدة (النسخ الاحتياطي والاستيراد)
  - إنشاء اختبارات للمكونات المحدثة
  - اختبار التكامل بين المكونات الجديدة
  - اختبار الأداء والاستجابة
  - _المتطلبات: جميع المتطلبات_

- [ ] 17. تحديث الوثائق وإرشادات النشر
  - تحديث ملفات README وإرشادات التثبيت
  - توثيق التغييرات في قاعدة البيانات
  - إنشاء دليل الترحيل للمطورين
  - تحديث متغيرات البيئة المطلوبة
  - _المتطلبات: جميع المتطلبات_
