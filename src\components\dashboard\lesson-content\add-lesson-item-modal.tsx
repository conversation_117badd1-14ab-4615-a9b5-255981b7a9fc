"use client";

import React, { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alog<PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { ChevronLeft, ChevronRight, Plus, Trash2, Upload, Mic, Play, Pause, Volume2, Bold, AlertCircle } from "lucide-react";
import { toast } from "sonner";
import { AudioRecorder, RecordingResult } from "@/utils/audio-recorder";

interface Part {
  id: string;
  title: string;
  order: number;
}

interface Lesson {
  id: string;
  title: string;
  partId?: string;
}

interface Color {
  id: string;
  name: string;
  hex: string;
  isDefault?: boolean;
}

interface LessonItemPart {
  text: string;
  color: string;
  fontWeight: 'light' | 'normal' | 'bold';
}

interface LessonItem {
  id: string;
  type: 'letter' | 'word' | 'sentence';
  order: number;
  parts: Array<{
    text: string;
    color: string;
    fontWeight?: 'light' | 'normal' | 'bold';
    audio?: string;
    spellingAudio?: string;
  }>;
  lesson: {
    id: string;
    title: string;
    part?: {
      id: string;
      title: string;
    };
  };
  backgroundColor?: string;
  audioUrl?: string;
  spellingAudioUrl?: string;
  createdAt: string;
  updatedAt: string;
}

interface AddLessonItemModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
  editItem?: LessonItem;
}

export function AddLessonItemModal({ open, onOpenChange, onSuccess, editItem }: AddLessonItemModalProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);

  // البيانات المطلوبة
  const [parts, setParts] = useState<Part[]>([]);
  const [lessons, setLessons] = useState<Lesson[]>([]);
  const [colors, setColors] = useState<Color[]>([]);

  // بيانات النموذج
  const [formData, setFormData] = useState({
    partId: "",
    lessonId: "",
    type: "" as "letter" | "word" | "sentence" | "",
    order: 1,
    parts: [] as LessonItemPart[],
    backgroundColor: "#ffffff",
    audioUrl: "",
    spellingAudioUrl: "",
  });

  // حالات التسجيل والرفع المحسنة
  const [isRecording, setIsRecording] = useState(false);
  const [recordingType, setRecordingType] = useState<"audio" | "spellingAudio" | null>(null);
  const [uploadingAudio, setUploadingAudio] = useState(false);
  const [uploadingSpellingAudio, setUploadingSpellingAudio] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [recordingInterval, setRecordingInterval] = useState<NodeJS.Timeout | null>(null);

  // حالات معاينة الصوت
  const [previewAudio, setPreviewAudio] = useState<{ [key: string]: string }>({});
  const [playingAudio, setPlayingAudio] = useState<string | null>(null);

  // مرجع AudioRecorder المحسن
  const audioRecorderRef = useRef<AudioRecorder | null>(null);
  const [isAudioSupported, setIsAudioSupported] = useState(true);
  const [audioError, setAudioError] = useState<string | null>(null);

  const totalSteps = 3;
  const progress = (currentStep / totalSteps) * 100;

  // جلب البيانات عند فتح النافذة
  useEffect(() => {
    if (open) {
      // فحص دعم تسجيل الصوت
      setIsAudioSupported(AudioRecorder.isSupported());
      setAudioError(null);

      fetchData();
      if (editItem) {
        loadEditData();
      } else {
        resetForm();
      }
    } else {
      // تنظيف التسجيل عند إغلاق النافذة
      if (isRecording && audioRecorderRef.current) {
        audioRecorderRef.current.cancelRecording();
        setIsRecording(false);
        setRecordingType(null);
        if (recordingInterval) {
          clearInterval(recordingInterval);
          setRecordingInterval(null);
        }
      }

      // تنظيف URLs المعاينة
      Object.values(previewAudio).forEach(url => {
        if (url) {
          URL.revokeObjectURL(url);
        }
      });
      setPreviewAudio({});
      setPlayingAudio(null);
    }
  }, [open, editItem]);

  // تنظيف الموارد عند إلغاء تحميل المكون
  useEffect(() => {
    return () => {
      if (recordingInterval) {
        clearInterval(recordingInterval);
      }
      if (audioRecorderRef.current) {
        audioRecorderRef.current.destroy();
      }
    };
  }, []);

  const fetchData = async () => {
    try {
      // جلب الأجزاء (Parts)
      try {
        const partsResponse = await fetch('/api/parts');
        if (partsResponse.ok) {
          const partsData = await partsResponse.json();
          setParts(partsData.parts || []);
        } else {
          console.warn('Parts API not available yet');
          setParts([]);
        }
      } catch (error) {
        console.warn('Parts API not implemented yet:', error);
        setParts([]);
      }

      // إزالة جلب الوحدات - لم تعد مطلوبة

      // جلب الدروس
      const lessonsResponse = await fetch('/api/lessons');
      const lessonsData = await lessonsResponse.json();
      setLessons(lessonsData.lessons || []);

      // جلب الألوان
      const colorsResponse = await fetch('/api/colors');
      const colorsData = await colorsResponse.json();
      setColors(colorsData.colors || []);

    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('حدث خطأ أثناء جلب البيانات');
    }
  };

  const resetForm = () => {
    setCurrentStep(1);

    // البحث عن اللون الافتراضي للخلفية من قاعدة البيانات
    const defaultBackgroundColor = colors.find(c => c.isDefault)?.hex ||
                                   colors.find(c => c.name?.toLowerCase().includes('أبيض'))?.hex ||
                                   colors.find(c => c.name?.toLowerCase().includes('white'))?.hex ||
                                   colors.find(c => c.hex === '#ffffff')?.hex ||
                                   colors[0]?.hex ||
                                   '#ffffff';

    setFormData({
      partId: "",
      lessonId: "",
      type: "",
      order: 1,
      parts: [],
      backgroundColor: defaultBackgroundColor,
      audioUrl: "",
      spellingAudioUrl: "",
    });
    setIsRecording(false);
    setRecordingType(null);
  };

  const loadEditData = () => {
    if (!editItem) return;

    console.log('🔄 تحميل بيانات التعديل:', editItem);
    
    setCurrentStep(1);
    
    // تحديد معرف الجزء من بيانات الدرس
    let partId = "";
    if (editItem.lesson.part?.id) {
      partId = editItem.lesson.part.id;
    } else {
      // البحث عن الجزء من قائمة الدروس
      const lesson = lessons.find(l => l.id === editItem.lesson.id);
      if (lesson?.partId) {
        partId = lesson.partId;
      }
    }
    
    setFormData({
      partId: partId,
      lessonId: editItem.lesson.id,
      type: editItem.type,
      order: editItem.order,
      parts: editItem.parts.map(part => ({
        text: part.text,
        color: part.color,
        fontWeight: part.fontWeight || 'normal'
      })),
      backgroundColor: editItem.backgroundColor || "#ffffff",
      audioUrl: editItem.audioUrl || "",
      spellingAudioUrl: editItem.spellingAudioUrl || "",
    });
    
    console.log('✅ تم تحميل بيانات التعديل بنجاح:', {
      partId,
      lessonId: editItem.lesson.id,
      type: editItem.type,
      partsCount: editItem.parts.length
    });
  };

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);

      // إعداد البيانات للإرسال
      const submitData = {
        lessonId: formData.lessonId,
        type: formData.type,
        order: formData.order,
        parts: formData.parts.map(part => ({
          text: part.text,
          color: part.color,
          fontWeight: part.fontWeight
        })),
        backgroundColor: formData.backgroundColor,
        audioUrl: formData.audioUrl,
        spellingAudioUrl: formData.spellingAudioUrl,
      };

      console.log('📤 إرسال بيانات المكون:', submitData);

      const isEditing = !!editItem;
      const url = isEditing ? `/api/lesson-items/${editItem.id}` : '/api/lesson-items';
      const method = isEditing ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `فشل في ${isEditing ? 'تحديث' : 'إنشاء'} المكون`);
      }

      const result = await response.json();

      // تحديث القائمة بدون إعادة تحميل
      if (typeof window !== 'undefined') {
        if (isEditing && (window as any).updateLessonItem) {
          (window as any).updateLessonItem(result.lessonItem);
        } else if (!isEditing && (window as any).addLessonItem) {
          (window as any).addLessonItem(result.lessonItem);
        }
      }

      toast.success(`تم ${isEditing ? 'تحديث' : 'إنشاء'} المكون بنجاح`);
      onOpenChange(false);
      // إعادة تعيين النموذج
      resetForm();
      // استدعاء onSuccess كاحتياطي
      onSuccess?.();

    } catch (error) {
      console.error(`Error ${editItem ? 'updating' : 'creating'} lesson item:`, error);
      toast.error(`حدث خطأ أثناء ${editItem ? 'تحديث' : 'إنشاء'} المكون: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`);
    } finally {
      setLoading(false);
    }
  };

  const addPart = () => {
    // البحث عن اللون الافتراضي بترتيب الأولوية
    const defaultColor = colors.find(c => c.isDefault)?.hex ||
                         colors.find(c => c.name?.toLowerCase().includes('أسود'))?.hex ||
                         colors.find(c => c.name?.toLowerCase().includes('black'))?.hex ||
                         colors.find(c => c.hex === '#000000')?.hex ||
                         colors[0]?.hex ||
                         '#000000';

    setFormData(prev => ({
      ...prev,
      parts: [...prev.parts, { text: '', color: defaultColor, fontWeight: 'light' as const }]
    }));
  };

  const removePart = (index: number) => {
    setFormData(prev => ({
      ...prev,
      parts: prev.parts.filter((_, i) => i !== index)
    }));
  };

  const updatePart = (index: number, field: keyof LessonItemPart, value: string) => {
    setFormData(prev => ({
      ...prev,
      parts: prev.parts.map((part, i) =>
        i === index ? { ...part, [field]: value } : part
      )
    }));
  };

  // دوال التسجيل الصوتي المحسنة
  const startRecording = async (type: 'audio' | 'spellingAudio') => {
    try {
      setAudioError(null);

      // فحص الدعم أولاً
      if (!isAudioSupported) {
        throw new Error('تسجيل الصوت غير مدعوم في هذا المتصفح');
      }

      // إنشاء مسجل جديد
      audioRecorderRef.current = new AudioRecorder();

      // بدء التسجيل
      await audioRecorderRef.current.startRecording({
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true,
        sampleRate: 44100
      });

      setRecordingType(type);
      setIsRecording(true);
      setRecordingTime(0);

      // بدء عداد الوقت
      const interval = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
      setRecordingInterval(interval);

      toast.success(`بدأ تسجيل ${type === 'audio' ? 'الصوت الطبيعي' : 'صوت التهجئة'}`);

    } catch (error) {
      console.error('Error starting recording:', error);

      const errorMessage = error instanceof Error ? error.message : 'خطأ غير معروف';
      setAudioError(errorMessage);
      toast.error(errorMessage);

      // تنظيف في حالة الخطأ
      setIsRecording(false);
      setRecordingType(null);
      if (recordingInterval) {
        clearInterval(recordingInterval);
        setRecordingInterval(null);
      }
    }
  };

  const stopRecording = async () => {
    if (!isRecording || !audioRecorderRef.current || !recordingType) {
      return;
    }

    try {
      toast.info('جاري معالجة التسجيل...');

      // إيقاف التسجيل والحصول على النتيجة
      const result: RecordingResult = await audioRecorderRef.current.stopRecording();

      console.log(`🎙️ تم التسجيل بنجاح:`, {
        duration: result.duration,
        size: result.blob.size,
        mimeType: result.mimeType
      });

      // حفظ URL للمعاينة
      setPreviewAudio(prev => ({
        ...prev,
        [recordingType]: result.url
      }));

      // رفع التسجيل تلقائياً
      try {
        // تحويل URL إلى Blob ثم إلى File
        const response = await fetch(result.url);
        const blob = await response.blob();

        const timestamp = Date.now();
        const fileName = `recording-${recordingType}-${timestamp}.webm`;
        const file = new File([blob], fileName, {
          type: blob.type || 'audio/webm',
          lastModified: timestamp
        });

        // رفع الملف تلقائياً
        await handleAudioUpload(file, recordingType);

        // إزالة المعاينة بعد الرفع الناجح
        setPreviewAudio(prev => {
          const updated = { ...prev };
          delete updated[recordingType];
          return updated;
        });

        toast.success(`تم التسجيل والرفع بنجاح! المدة: ${Math.round(result.duration / 1000)} ثانية`);
      } catch (uploadError) {
        console.error('Error auto-uploading recording:', uploadError);
        toast.success(`تم التسجيل بنجاح! المدة: ${Math.round(result.duration / 1000)} ثانية`);
        toast.info('يمكنك رفع التسجيل يدوياً باستخدام زر الرفع');
      }

    } catch (error) {
      console.error('Error stopping recording:', error);
      const errorMessage = error instanceof Error ? error.message : 'خطأ في إيقاف التسجيل';
      setAudioError(errorMessage);
      toast.error(errorMessage);
    } finally {
      // تنظيف الحالة
      setIsRecording(false);
      setRecordingType(null);
      if (recordingInterval) {
        clearInterval(recordingInterval);
        setRecordingInterval(null);
      }
    }
  };

  const toggleRecording = async (type: 'audio' | 'spellingAudio') => {
    if (isRecording && recordingType === type) {
      await stopRecording();
    } else if (!isRecording) {
      await startRecording(type);
    }
  };

  const formatRecordingTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // دوال معاينة الصوت - محسنة لدعم الصوت الموجود والمسجل
  const playPreviewAudio = (_audioUrl: string, type: string) => {
    if (playingAudio === type) {
      // إيقاف التشغيل
      const audio = document.getElementById(`audio-${type}`) as HTMLAudioElement;
      if (audio) {
        audio.pause();
        audio.currentTime = 0;
      }
      setPlayingAudio(null);
    } else {
      // بدء التشغيل
      setPlayingAudio(type);
      const audio = document.getElementById(`audio-${type}`) as HTMLAudioElement;
      if (audio) {
        audio.play().catch(console.error);
      }
    }
  };

  // تشغيل الصوت الموجود (للملفات المرفوعة مسبقاً)
  const playExistingAudio = (audioUrl: string, type: 'audio' | 'spellingAudio') => {
    if (playingAudio === type) {
      // إيقاف التشغيل
      const audio = document.getElementById(`existing-audio-${type}`) as HTMLAudioElement;
      if (audio) {
        audio.pause();
        audio.currentTime = 0;
      }
      setPlayingAudio(null);
    } else {
      // بدء التشغيل
      setPlayingAudio(type);
      const audio = document.getElementById(`existing-audio-${type}`) as HTMLAudioElement;
      if (audio) {
        audio.play().catch(console.error);
      }
    }
  };

  // إيقاف الصوت تماماً (زر Stop)
  const stopAudio = (type: string) => {
    const previewAudio = document.getElementById(`audio-${type}`) as HTMLAudioElement;
    const existingAudio = document.getElementById(`existing-audio-${type}`) as HTMLAudioElement;

    if (previewAudio) {
      previewAudio.pause();
      previewAudio.currentTime = 0;
    }

    if (existingAudio) {
      existingAudio.pause();
      existingAudio.currentTime = 0;
    }

    setPlayingAudio(null);
  };

  const handleAudioEnded = (type: string) => {
    setPlayingAudio(null);
  };

  // رفع الصوت المسجل
  const uploadRecordedAudio = async (type: 'audio' | 'spellingAudio') => {
    const previewUrl = previewAudio[type];
    if (!previewUrl) {
      toast.error('لا يوجد تسجيل صوتي للرفع');
      return;
    }

    try {
      console.log(`🎵 بدء رفع التسجيل الصوتي ${type}`);
      console.log(`📎 Preview URL: ${previewUrl}`);

      // تحويل URL إلى Blob ثم إلى File
      const response = await fetch(previewUrl);
      if (!response.ok) {
        throw new Error(`فشل في جلب البيانات الصوتية: ${response.status}`);
      }

      const blob = await response.blob();
      console.log(`📦 Blob size: ${blob.size} bytes, type: ${blob.type}`);

      if (blob.size === 0) {
        throw new Error('الملف الصوتي فارغ');
      }

      // إنشاء ملف بتنسيق صحيح
      const timestamp = Date.now();
      const fileName = `recording-${type}-${timestamp}.webm`;
      const file = new File([blob], fileName, {
        type: blob.type || 'audio/webm',
        lastModified: timestamp
      });

      console.log(`📄 File created: ${file.name}, size: ${file.size}, type: ${file.type}`);

      // رفع الملف
      await handleAudioUpload(file, type);

      // إزالة المعاينة بعد الرفع الناجح
      setPreviewAudio(prev => {
        const updated = { ...prev };
        delete updated[type];
        return updated;
      });

      console.log(`✅ تم رفع ${type} بنجاح وإزالة المعاينة`);

    } catch (error) {
      console.error(`❌ Error uploading recorded audio ${type}:`, error);
      const errorMessage = error instanceof Error ? error.message : 'خطأ غير معروف';
      toast.error(`فشل في رفع التسجيل الصوتي: ${errorMessage}`);
    }
  };

  // وظيفة رفع الملف الصوتي
  const handleAudioUpload = async (file: File, type: 'audio' | 'spellingAudio') => {
    try {
      console.log(`📤 بدء رفع ملف ${type}:`, {
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified
      });

      if (type === 'audio') {
        setUploadingAudio(true);
      } else {
        setUploadingSpellingAudio(true);
      }

      // التحقق الشامل من صحة الملف
      if (!file) {
        throw new Error('لم يتم العثور على ملف صوتي');
      }

      if (!(file instanceof File)) {
        throw new Error('الكائن المرسل ليس ملف صالح');
      }

      if (file.size === 0) {
        throw new Error('الملف فارغ');
      }

      if (file.size > 10 * 1024 * 1024) { // 10MB
        throw new Error('حجم الملف كبير جداً (الحد الأقصى 10MB)');
      }

      // إنشاء FormData مع تسجيل مفصل
      const uploadFormData = new FormData();
      uploadFormData.append('file', file, file.name);

      // إضافة معلومات إضافية
      uploadFormData.append('type', 'audio');
      uploadFormData.append('category', type);
      uploadFormData.append('originalName', file.name);
      uploadFormData.append('fileSize', file.size.toString());

      console.log('📤 FormData entries:');
      for (const [key, value] of uploadFormData.entries()) {
        if (value instanceof File) {
          console.log(`  ${key}: File(${value.name}, ${value.size} bytes, ${value.type})`);
        } else {
          console.log(`  ${key}: ${value}`);
        }
      }

      const response = await fetch('/api/upload/audio', {
        method: 'POST',
        body: uploadFormData,
      });

      console.log(`📡 Response status: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        let errorMessage = `HTTP ${response.status}`;
        let errorDetails = '';

        try {
          const errorText = await response.text();
          console.log(`📄 Response text: ${errorText}`);

          try {
            const errorData = JSON.parse(errorText);
            errorMessage = errorData.error || errorData.message || errorMessage;
            errorDetails = errorData.details || '';
          } catch {
            errorMessage = errorText || errorMessage;
          }
        } catch {
          errorMessage = `HTTP ${response.status} - ${response.statusText}`;
        }

        throw new Error(`${errorMessage}${errorDetails ? ` (${errorDetails})` : ''}`);
      }

      const result = await response.json();
      console.log(`✅ تم رفع ملف ${type} بنجاح:`, result);

      // تحديث بيانات النموذج
      const audioUrlField = type === 'audio' ? 'audioUrl' : 'spellingAudioUrl';
      const audioUrl = result.url || result.path || result.fileUrl || result.audioUrl;

      if (!audioUrl) {
        console.error('❌ Response missing audio URL:', result);
        throw new Error('لم يتم إرجاع رابط الملف من الخادم');
      }

      setFormData(prev => {
        const updated = {
          ...prev,
          [audioUrlField]: audioUrl
        };
        console.log(`🔄 تحديث ${audioUrlField}:`, audioUrl);
        return updated;
      });

      toast.success(`تم رفع ${type === 'audio' ? 'الصوت الطبيعي' : 'صوت التهجئة'} بنجاح`);

    } catch (error) {
      console.error(`❌ Error uploading audio ${type}:`, error);
      const errorMessage = error instanceof Error ? error.message : 'خطأ غير معروف';
      toast.error(`حدث خطأ أثناء رفع الملف: ${errorMessage}`);
    } finally {
      if (type === 'audio') {
        setUploadingAudio(false);
      } else {
        setUploadingSpellingAudio(false);
      }
    }
  };

  const canProceedToStep2 = formData.lessonId && formData.type;
  const canProceedToStep3 = formData.parts.length > 0 && formData.parts.every(part => part.text.trim());
  const canSubmit = canProceedToStep3;

  const getStepTitle = (step: number) => {
    switch (step) {
      case 1: return 'تحديد الموقع';
      case 2: return 'تخصيص النص والألوان';
      case 3: return 'إعدادات الصوت';
      default: return '';
    }
  };

  const getStepDescription = (step: number) => {
    switch (step) {
      case 1: return 'اختر الجزء والدرس ونوع المكون وترتيبه';
      case 2: return 'أضف النصوص وحدد الألوان المناسبة لكل جزء من المكون';
      case 3: return 'ارفع الملفات الصوتية أو سجل الأصوات للمكون (اختياري)';
      default: return '';
    }
  };

  const filteredLessons = lessons.filter(lesson =>
    !formData.partId || lesson.partId === formData.partId
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>

      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl">
            {editItem ? 'تحرير المكون' : 'إضافة مكون جديد'}
          </DialogTitle>
        </DialogHeader>

        {/* مؤشر تقدم الخطوات المحسن */}
        <div className="bg-muted/30 rounded-lg p-4 mb-6" dir="rtl">
          {/* عنوان التقدم */}
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-primary">
              {getStepTitle(currentStep)}
            </h3>
            <span className="text-sm text-muted-foreground bg-background px-3 py-1 rounded-full">
              الخطوة {currentStep} من {totalSteps}
            </span>
          </div>

          {/* مؤشر الخطوات البصري */}
          <div className="flex items-center justify-between mb-3">
            {[1, 2, 3].map((step, index) => (
              <div key={step} className="flex items-center flex-1">
                {/* دائرة الخطوة */}
                <div className="flex items-center">
                  <div
                    className={`
                      w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold transition-all duration-300
                      ${step < currentStep
                        ? 'bg-green-500 text-white'
                        : step === currentStep
                        ? 'bg-primary text-primary-foreground ring-4 ring-primary/20'
                        : 'bg-muted text-muted-foreground'
                      }
                    `}
                  >
                    {step < currentStep ? (
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    ) : (
                      step
                    )}
                  </div>

                  {/* عنوان الخطوة */}
                  <div className="mr-3 text-right">
                    <div className={`text-sm font-medium ${step === currentStep ? 'text-primary' : step < currentStep ? 'text-green-600' : 'text-muted-foreground'}`}>
                      {getStepTitle(step)}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {step < currentStep ? 'مكتملة' : step === currentStep ? 'جارية' : 'قادمة'}
                    </div>
                  </div>
                </div>

                {/* خط الربط */}
                {index < 2 && (
                  <div className="flex-1 mx-4">
                    <div
                      className={`h-1 rounded-full transition-all duration-300 ${
                        step < currentStep ? 'bg-green-500' : 'bg-muted'
                      }`}
                    />
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* شريط التقدم الإجمالي */}
          <div className="space-y-2">
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>التقدم الإجمالي</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>

          {/* وصف الخطوة الحالية */}
          <div className="mt-3 p-3 bg-background rounded-md border-r-4 border-primary">
            <p className="text-sm text-muted-foreground">
              {getStepDescription(currentStep)}
            </p>
          </div>
        </div>

        {/* محتوى الخطوات */}
        <div dir="rtl" className="space-y-6">
          {/* الخطوة الأولى: تحديد الموقع */}
          {currentStep === 1 && (
            <Card>
              <CardHeader>
                <CardTitle>تحديد موقع المكون والمعلومات الأساسية</CardTitle>
                <CardDescription>
                  حدد الجزء والدرس ونوع المكون وترتيبه في التسلسل التعليمي
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* الحقول الأساسية بترتيب محدد */}
                <div className="space-y-4">
                  {/* الصف الأول: الجزء والدرس */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {/* اختيار الجزء */}
                  <div className="space-y-2">
                    <Label>الجزء (القسم) *</Label>
                    <Select
                      value={formData.partId}
                      onValueChange={(value) => {
                        setFormData(prev => ({ ...prev, partId: value, lessonId: "" }));
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="اختر الجزء" />
                      </SelectTrigger>
                      <SelectContent>
                        {parts.length === 0 ? (
                          <div className="p-2 text-center text-muted-foreground">
                            <p className="text-sm">لا توجد أجزاء متاحة</p>
                            <p className="text-xs mt-1">سيتم إضافة هذه الميزة قريباً</p>
                          </div>
                        ) : (
                          parts.map(part => (
                            <SelectItem key={part.id} value={part.id}>
                              {part.title}
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                  </div>

                    {/* اختيار الدرس */}
                    <div className="space-y-2">
                      <Label>الدرس *</Label>
                      <Select
                        value={formData.lessonId}
                        onValueChange={(value) => setFormData(prev => ({ ...prev, lessonId: value }))}
                        disabled={!formData.partId && parts.length > 0}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="اختر الدرس" />
                        </SelectTrigger>
                        <SelectContent>
                          {filteredLessons.map(lesson => (
                            <SelectItem key={lesson.id} value={lesson.id}>
                              {lesson.title}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* نوع المكون */}
                    <div className="space-y-2">
                      <Label>النوع *</Label>
                      <Select
                        value={formData.type}
                        onValueChange={(value: "letter" | "word" | "sentence") =>
                          setFormData(prev => ({ ...prev, type: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="اختر النوع" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="letter">حرف</SelectItem>
                          <SelectItem value="word">كلمة</SelectItem>
                          <SelectItem value="sentence">جملة</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* الصف الثالث: ترتيب المكون */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>الترتيب *</Label>
                      <Input
                        type="number"
                        min="1"
                        value={formData.order}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          order: parseInt(e.target.value) || 1
                        }))}
                        placeholder="ترتيب المكون في الدرس"
                      />
                      <p className="text-xs text-muted-foreground">
                        رقم ترتيب المكون ضمن الدرس (1، 2، 3...)
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* الخطوة الثانية: تخصيص النص والألوان */}
          {currentStep === 2 && (
            <Card>
              <CardHeader>
                <CardTitle>تخصيص النص والألوان</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* لون الخلفية */}
                <div className="space-y-2">
                  <Label>لون خلفية البطاقة</Label>
                  <Select
                    value={formData.backgroundColor}
                    onValueChange={(value) => setFormData(prev => ({
                      ...prev,
                      backgroundColor: value
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue>
                        {formData.backgroundColor && (
                          <div className="flex items-center gap-2">
                            <div
                              className="w-4 h-4 rounded border"
                              style={{ backgroundColor: formData.backgroundColor }}
                            />
                            {colors.find(c => c.hex === formData.backgroundColor)?.name || 'لون مخصص'}
                          </div>
                        )}
                      </SelectValue>
                    </SelectTrigger>
                    <SelectContent>
                      {colors.length === 0 ? (
                        <div className="p-4 text-center text-muted-foreground">
                          <p className="text-sm mb-2">لا توجد ألوان متاحة</p>
                          <p className="text-xs">يرجى إضافة ألوان من صفحة إدارة الألوان</p>
                          <a
                            href="/dashboard/admin/colors"
                            target="_blank"
                            className="text-primary hover:underline text-xs mt-2 inline-block"
                          >
                            إدارة الألوان ←
                          </a>
                        </div>
                      ) : (
                        colors.map(color => (
                          <SelectItem key={color.id} value={color.hex}>
                            <div className="flex items-center gap-2">
                              <div
                                className="w-4 h-4 rounded border border-gray-300"
                                style={{ backgroundColor: color.hex }}
                              />
                              <span>{color.name}</span>
                              <span className="text-xs text-muted-foreground ml-auto">
                                {color.hex}
                              </span>
                            </div>
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                </div>

                {/* أجزاء النص */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label>أجزاء النص *</Label>
                    <Button onClick={addPart} size="sm">
                      <Plus className="h-4 w-4 mr-1" />
                      إضافة جزء
                    </Button>
                  </div>

                  {formData.parts.map((part, index) => (
                    <Card key={index} className="p-4">
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                        <div className="space-y-2">
                          <Label>النص</Label>
                          <Input
                            value={part.text}
                            onChange={(e) => updatePart(index, 'text', e.target.value)}
                            placeholder="أدخل النص"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label>اللون</Label>
                          <Select
                            value={part.color}
                            onValueChange={(value) => updatePart(index, 'color', value)}
                          >
                            <SelectTrigger>
                              <SelectValue>
                                {part.color && (
                                  <div className="flex items-center gap-2">
                                    <div
                                      className="w-4 h-4 rounded border"
                                      style={{ backgroundColor: part.color }}
                                    />
                                    {colors.find(c => c.hex === part.color)?.name || 'لون مخصص'}
                                  </div>
                                )}
                              </SelectValue>
                            </SelectTrigger>
                            <SelectContent>
                              {colors.length === 0 ? (
                                <div className="p-2 text-center text-muted-foreground">
                                  <p>لا توجد ألوان متاحة</p>
                                  <p className="text-xs mt-1">يرجى إضافة ألوان من صفحة إدارة الألوان</p>
                                </div>
                              ) : (
                                colors.map(color => (
                                  <SelectItem key={color.id} value={color.hex}>
                                    <div className="flex items-center gap-2">
                                      <div
                                        className="w-4 h-4 rounded border border-gray-300"
                                        style={{ backgroundColor: color.hex }}
                                      />
                                      <span>{color.name}</span>
                                      <span className="text-xs text-muted-foreground ml-auto">
                                        {color.hex}
                                      </span>
                                    </div>
                                  </SelectItem>
                                ))
                              )}
                            </SelectContent>
                          </Select>

                          {/* رابط إدارة الألوان */}
                          {/* <div className="flex justify-end">
                            <a
                              href="/dashboard/admin/colors"
                              target="_blank"
                              className="text-xs text-primary hover:underline flex items-center gap-1"
                            >
                              إدارة الألوان
                              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                              </svg>
                            </a>
                          </div> */}
                        </div>

                        <div className="space-y-2">
                          <Label className="flex items-center gap-2">
                            <Bold className="h-4 w-4" />
                            وزن الخط
                          </Label>
                          <Select
                            value={part.fontWeight}
                            onValueChange={(value: 'light' | 'normal' | 'bold') => updatePart(index, 'fontWeight', value)}
                          >
                            <SelectTrigger>
                              <SelectValue>
                                <div className="flex items-center gap-2">
                                  <span style={{ fontWeight: part.fontWeight === 'light' ? '300' : part.fontWeight === 'bold' ? '700' : '400' }}>
                                    {part.fontWeight === 'light' ? 'خفيف' : part.fontWeight === 'bold' ? 'عريض' : 'عادي'}
                                  </span>
                                </div>
                              </SelectValue>
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="light">
                                <div className="flex items-center gap-2">
                                  <span style={{ fontWeight: '300' }}>خفيف</span>
                                  <span className="text-xs text-muted-foreground">Light</span>
                                </div>
                              </SelectItem>
                              <SelectItem value="normal">
                                <div className="flex items-center gap-2">
                                  <span style={{ fontWeight: '400' }}>عادي</span>
                                  <span className="text-xs text-muted-foreground">Normal</span>
                                </div>
                              </SelectItem>
                              <SelectItem value="bold">
                                <div className="flex items-center gap-2">
                                  <span style={{ fontWeight: '700' }}>عريض</span>
                                  <span className="text-xs text-muted-foreground">Bold</span>
                                </div>
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => removePart(index)}
                          disabled={formData.parts.length === 1}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </Card>
                  ))}

                  {formData.parts.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      <p>لم يتم إضافة أي أجزاء بعد</p>
                      {colors.length === 0 ? (
                        <div className="mt-4 p-4 bg-amber-50 border border-amber-200 rounded-lg">
                          <p className="text-amber-800 text-sm mb-2">
                            ⚠️ لا توجد ألوان متاحة في النظام
                          </p>
                          <a
                            href="/dashboard/admin/colors"
                            target="_blank"
                            className="text-amber-700 hover:text-amber-900 underline text-sm"
                          >
                            يرجى إضافة ألوان أولاً من صفحة إدارة الألوان
                          </a>
                        </div>
                      ) : (
                        <Button onClick={addPart} className="mt-2">
                          <Plus className="h-4 w-4 mr-1" />
                          إضافة الجزء الأول
                        </Button>
                      )}
                    </div>
                  )}
                </div>

                {/* معاينة */}
                {formData.parts.length > 0 && (
                  <div className="space-y-2">
                    <Label>معاينة</Label>
                    <Card
                      className="p-6 text-center"
                      style={{ backgroundColor: formData.backgroundColor }}
                    >
                      <div className="text-3xl text-center" style={{ direction: 'rtl', lineHeight: '1.2' }}>
                        <span style={{ display: 'inline-block' }}>
                          {formData.parts.map((part, index) => (
                            <span
                              key={index}
                              style={{
                                color: part.color,
                                fontWeight: part.fontWeight === 'light' ? '300' : part.fontWeight === 'bold' ? '700' : '400',
                                margin: 0,
                                padding: 0,
                                letterSpacing: 'normal'
                              }}
                            >
                              {part.text}
                            </span>
                          ))}
                        </span>
                      </div>
                    </Card>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* الخطوة الثالثة: إعدادات الصوت */}
          {currentStep === 3 && (
            <Card>
              <CardHeader>
                <CardTitle>إعدادات الصوت للمكون</CardTitle>
                <CardDescription>
                  إضافة ملفات صوتية للمكون كاملاً
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* معاينة المكون */}
                <div className="p-4 bg-muted rounded-lg">
                  <Label className="text-sm font-medium mb-2 block">معاينة المكون:</Label>
                  <div className="text-2xl text-center" style={{ direction: 'rtl', lineHeight: '1.2' }}>
                    <span style={{ display: 'inline-block' }}>
                      {formData.parts.map((part, index) => (
                        <span
                          key={index}
                          style={{
                            color: part.color,
                            fontWeight: part.fontWeight === 'light' ? '300' : part.fontWeight === 'bold' ? '700' : '400',
                            margin: 0,
                            padding: 0,
                            letterSpacing: 'normal'
                          }}
                        >
                          {part.text}
                        </span>
                      ))}
                    </span>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* الصوت الطبيعي */}
                  <Card className="p-4">
                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <Volume2 className="h-5 w-5 text-primary" />
                        <Label className="font-medium">الصوت الطبيعي</Label>
                      </div>

                      <div className="space-y-3">
                        {/* رفع ملف */}
                        <div>
                          <Label htmlFor="audio-upload" className="text-sm">رفع ملف صوتي</Label>
                          <input
                            id="audio-upload"
                            type="file"
                            accept="audio/*"
                            disabled={uploadingAudio}
                            className="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-primary-foreground hover:file:bg-primary/80 disabled:opacity-50"
                            onChange={(e) => {
                              const file = e.target.files?.[0];
                              if (file) {
                                handleAudioUpload(file, 'audio');
                              }
                            }}
                          />
                          {uploadingAudio && (
                            <div className="mt-2 text-xs text-blue-600 flex items-center gap-2">
                              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600"></div>
                              جاري رفع الملف...
                            </div>
                          )}
                        </div>

                        {/* تحذير عدم الدعم */}
                        {!isAudioSupported && (
                          <div className="bg-yellow-50 border border-yellow-200 p-3 rounded-lg">
                            <div className="flex items-start gap-2">
                              <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                              <div className="text-yellow-800 text-sm">
                                <p className="font-medium mb-1">تسجيل الصوت غير مدعوم</p>
                                <p className="text-xs">
                                  متصفحك لا يدعم تسجيل الصوت. يمكنك رفع ملف صوتي بدلاً من ذلك.
                                </p>
                              </div>
                            </div>
                          </div>
                        )}

                        {/* رسالة خطأ */}
                        {audioError && (
                          <div className="bg-red-50 border border-red-200 p-3 rounded-lg">
                            <div className="flex items-start gap-2">
                              <AlertCircle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
                              <div className="text-red-800 text-sm">
                                <p className="font-medium mb-1">خطأ في التسجيل</p>
                                <p className="text-xs">{audioError}</p>
                              </div>
                            </div>
                          </div>
                        )}

                        {/* أزرار التحكم */}
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => toggleRecording('audio')}
                            disabled={!isAudioSupported}
                            className={isRecording && recordingType === 'audio' ? 'bg-red-100 text-red-700' : ''}
                          >
                            <Mic className="h-4 w-4 mr-1" />
                            {isRecording && recordingType === 'audio' ? 'إيقاف التسجيل' : 'تسجيل'}
                          </Button>

                          {/* عداد الوقت أثناء التسجيل */}
                          {isRecording && recordingType === 'audio' && (
                            <div className="flex items-center gap-2 text-red-600 text-sm">
                              <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                              {formatRecordingTime(recordingTime)}
                            </div>
                          )}

                          {/* معاينة الصوت المسجل */}
                          {previewAudio.audio && (
                            <div className="space-y-2">
                              <div className="flex items-center gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => playPreviewAudio(previewAudio.audio, 'audio')}
                                  className="flex items-center gap-2"
                                >
                                  {playingAudio === 'audio' ? (
                                    <Pause className="h-4 w-4" />
                                  ) : (
                                    <Play className="h-4 w-4" />
                                  )}
                                  {playingAudio === 'audio' ? 'إيقاف' : 'معاينة'}
                                </Button>
                                <Button
                                  variant="default"
                                  size="sm"
                                  onClick={() => uploadRecordedAudio('audio')}
                                  disabled={uploadingAudio}
                                  className="flex items-center gap-2"
                                >
                                  <Upload className="h-4 w-4" />
                                  {uploadingAudio ? 'جاري الرفع...' : 'رفع التسجيل'}
                                </Button>
                              </div>
                              <audio
                                id="audio-audio"
                                src={previewAudio.audio}
                                onEnded={() => handleAudioEnded('audio')}
                                className="hidden"
                              />
                            </div>
                          )}

                          {formData.audioUrl && (
                            <div className="flex items-center gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => playExistingAudio(formData.audioUrl, 'audio')}
                                className="flex items-center gap-2"
                              >
                                {playingAudio === 'audio' ? (
                                  <Pause className="h-4 w-4" />
                                ) : (
                                  <Play className="h-4 w-4" />
                                )}
                                {playingAudio === 'audio' ? 'إيقاف' : 'تشغيل'}
                              </Button>
                              {playingAudio === 'audio' && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => stopAudio('audio')}
                                  className="flex items-center gap-2"
                                >
                                  <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 24 24">
                                    <rect x="6" y="4" width="4" height="16" />
                                    <rect x="14" y="4" width="4" height="16" />
                                  </svg>
                                  إيقاف تام
                                </Button>
                              )}
                              <audio
                                id="existing-audio-audio"
                                src={formData.audioUrl}
                                onEnded={() => handleAudioEnded('audio')}
                                className="hidden"
                              />
                            </div>
                          )}
                        </div>

                        {formData.audioUrl && (
                          <div className="text-xs text-green-600 bg-green-50 p-2 rounded border border-green-200">
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                              <span>✅ تم رفع الملف الصوتي بنجاح</span>
                            </div>
                            <div className="text-xs text-green-500 mt-1 truncate">
                              {formData.audioUrl}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </Card>

                  {/* صوت التهجئة */}
                  <Card className="p-4">
                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <Volume2 className="h-5 w-5 text-secondary" />
                        <Label className="font-medium">صوت التهجئة</Label>
                      </div>

                      <div className="space-y-3">
                        {/* رفع ملف */}
                        <div>
                          <Label htmlFor="spelling-audio-upload" className="text-sm">رفع ملف صوتي للتهجئة</Label>
                          <input
                            id="spelling-audio-upload"
                            type="file"
                            accept="audio/*"
                            disabled={uploadingSpellingAudio}
                            className="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-secondary file:text-secondary-foreground hover:file:bg-secondary/80 disabled:opacity-50"
                            onChange={(e) => {
                              const file = e.target.files?.[0];
                              if (file) {
                                handleAudioUpload(file, 'spellingAudio');
                              }
                            }}
                          />
                          {uploadingSpellingAudio && (
                            <div className="mt-2 text-xs text-purple-600 flex items-center gap-2">
                              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-purple-600"></div>
                              جاري رفع ملف التهجئة...
                            </div>
                          )}
                        </div>

                        {/* أزرار التحكم */}
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => toggleRecording('spellingAudio')}
                            disabled={!isAudioSupported}
                            className={isRecording && recordingType === 'spellingAudio' ? 'bg-red-100 text-red-700' : ''}
                          >
                            <Mic className="h-4 w-4 mr-1" />
                            {isRecording && recordingType === 'spellingAudio' ? 'إيقاف التسجيل' : 'تسجيل'}
                          </Button>

                          {/* عداد الوقت أثناء التسجيل */}
                          {isRecording && recordingType === 'spellingAudio' && (
                            <div className="flex items-center gap-2 text-red-600 text-sm">
                              <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                              {formatRecordingTime(recordingTime)}
                            </div>
                          )}

                          {/* معاينة الصوت المسجل */}
                          {previewAudio.spellingAudio && (
                            <div className="space-y-2">
                              <div className="flex items-center gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => playPreviewAudio(previewAudio.spellingAudio, 'spellingAudio')}
                                  className="flex items-center gap-2"
                                >
                                  {playingAudio === 'spellingAudio' ? (
                                    <Pause className="h-4 w-4" />
                                  ) : (
                                    <Play className="h-4 w-4" />
                                  )}
                                  {playingAudio === 'spellingAudio' ? 'إيقاف' : 'معاينة'}
                                </Button>
                                <Button
                                  variant="default"
                                  size="sm"
                                  onClick={() => uploadRecordedAudio('spellingAudio')}
                                  disabled={uploadingSpellingAudio}
                                  className="flex items-center gap-2"
                                >
                                  <Upload className="h-4 w-4" />
                                  {uploadingSpellingAudio ? 'جاري الرفع...' : 'رفع التسجيل'}
                                </Button>
                              </div>
                              <audio
                                id="audio-spellingAudio"
                                src={previewAudio.spellingAudio}
                                onEnded={() => handleAudioEnded('spellingAudio')}
                                className="hidden"
                              />
                            </div>
                          )}

                          {formData.spellingAudioUrl && (
                            <div className="flex items-center gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => playExistingAudio(formData.spellingAudioUrl, 'spellingAudio')}
                                className="flex items-center gap-2"
                              >
                                {playingAudio === 'spellingAudio' ? (
                                  <Pause className="h-4 w-4" />
                                ) : (
                                  <Play className="h-4 w-4" />
                                )}
                                {playingAudio === 'spellingAudio' ? 'إيقاف' : 'تشغيل'}
                              </Button>
                              {playingAudio === 'spellingAudio' && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => stopAudio('spellingAudio')}
                                  className="flex items-center gap-2"
                                >
                                  <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 24 24">
                                    <rect x="6" y="4" width="4" height="16" />
                                    <rect x="14" y="4" width="4" height="16" />
                                  </svg>
                                  إيقاف تام
                                </Button>
                              )}
                              <audio
                                id="existing-audio-spellingAudio"
                                src={formData.spellingAudioUrl}
                                onEnded={() => handleAudioEnded('spellingAudio')}
                                className="hidden"
                              />
                            </div>
                          )}
                        </div>

                        {formData.spellingAudioUrl && (
                          <div className="text-xs text-green-600 bg-green-50 p-2 rounded border border-green-200">
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                              <span>✅ تم رفع ملف التهجئة بنجاح</span>
                            </div>
                            <div className="text-xs text-green-500 mt-1 truncate">
                              {formData.spellingAudioUrl}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </Card>
                </div>

                {/* ملاحظات */}
                <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg">
                  <div className="flex items-start gap-2">
                    <div className="text-blue-600 mt-0.5">💡</div>
                    <div className="text-blue-800 text-sm">
                      <p className="font-medium mb-1">نصائح لتسجيل الصوت:</p>
                      <ul className="list-disc list-inside space-y-1 text-xs">
                        <li>تأكد من وجودك في مكان هادئ</li>
                        <li>اقترب من الميكروفون للحصول على صوت واضح</li>
                        <li>انطق الكلمات ببطء ووضوح</li>
                        <li>يمكنك إعادة التسجيل إذا لم تكن راضياً عن النتيجة</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div className="text-sm text-muted-foreground bg-muted p-3 rounded">
                  <p><strong>ملاحظة:</strong> يمكنك إضافة أو تعديل الأصوات لاحقاً من خلال تحرير المكون</p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* أزرار التنقل */}
        <div className="flex justify-between items-center pt-4 border-t">
          {/* الأزرار الرئيسية - تظهر على اليمين في RTL */}
          <div dir="rtl" className="flex items-center gap-2">
            {currentStep < totalSteps ? (
              <Button
                onClick={handleNext}
                disabled={
                  (currentStep === 1 && !canProceedToStep2) ||
                  (currentStep === 2 && !canProceedToStep3)
                }
                className="flex items-center gap-2"
              >
                <ChevronRight className="h-4 w-4" />
                التالي
              </Button>
            ) : (
              <Button
                onClick={handleSubmit}
                disabled={!canSubmit || loading}
                className="flex items-center gap-2"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    {editItem ? 'جاري التحديث...' : 'جاري الإنشاء...'}
                  </>
                ) : (
                  editItem ? 'تحديث المكون' : 'إنشاء المكون'
                )}
              </Button>
            )}
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              إلغاء
            </Button>

          </div>
          {/* زر السابق - يظهر على اليسار في RTL */}
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentStep === 1}
            className="flex items-center gap-2"
          >
            السابق
            <ChevronLeft className="h-4 w-4" />
          </Button>

        </div>
      </DialogContent>
    </Dialog>
  );
}
