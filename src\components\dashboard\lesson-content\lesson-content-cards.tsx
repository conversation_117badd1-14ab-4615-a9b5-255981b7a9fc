'use client';

import React, { useEffect } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Edit, Trash2, Volume2 } from 'lucide-react';
import '@/styles/arabic-font-simple.css';
import { <PERSON>ont<PERSON>he<PERSON> } from '@/utils/font-checker';

interface LessonItemPart {
  text: string;
  color: string;
  fontWeight?: 'light' | 'normal' | 'bold';
}

interface LessonItem {
  id: string;
  type: 'letter' | 'word' | 'sentence';
  order: number;
  parts: LessonItemPart[];
  lesson: {
    id: string;
    title: string;
    unit?: {
      id: string;
      title: string;
    };
  };
  backgroundColor?: string;
  audioUrl?: string;
  spellingAudioUrl?: string;
  createdAt: string;
  updatedAt: string;
}

interface LessonContentCardsProps {
  items: LessonItem[];
  viewMode: 'grid' | 'list';
  onEdit?: (item: LessonItem) => void;
  onDelete?: (item: LessonItem) => void;
  playAudio?: (url: string, type: 'normal' | 'spelling', itemId?: string) => void;
  stopAudio?: () => void;
  playingAudioId?: string | null;
}

// مكون البطاقة الفردية للعرض الشبكي
const GridCard: React.FC<{
  item: LessonItem;
  onEdit?: (item: LessonItem) => void;
  onDelete?: (item: LessonItem) => void;
  playAudio?: (url: string, type: 'normal' | 'spelling', itemId?: string) => void;
  stopAudio?: () => void;
  playingAudioId?: string | null;
}> = ({ item, onEdit, onDelete, playAudio, stopAudio, playingAudioId }) => {
  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'letter': return 'حرف';
      case 'word': return 'كلمة';
      case 'sentence': return 'جملة';
      default: return type;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'letter': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'word': return 'bg-green-100 text-green-800 border-green-200';
      case 'sentence': return 'bg-purple-100 text-purple-800 border-purple-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <Card
      className="group relative overflow-hidden transition-all duration-300 hover:shadow-xl hover:scale-[1.02] border-2 hover:border-primary/30"
      style={{
        backgroundColor: item.backgroundColor || undefined,
        borderColor: item.backgroundColor && item.backgroundColor !== '#ffffff' ? 'rgba(0,0,0,0.15)' : undefined
      }}
    >
      {/* أزرار الإجراءات - مرئية دائماً في الزاوية العلوية اليسرى */}
      <div className="absolute top-3 left-3 z-10 flex gap-1">
        <Button
          variant="outline"
          size="icon"
          onClick={(e) => {
            e.stopPropagation();
            onEdit?.(item);
          }}
          className="h-8 w-8 bg-background/95 hover:bg-primary hover:text-primary-foreground border-border hover:border-primary shadow-lg backdrop-blur-sm transition-all duration-200"
          title="تحرير المكون"
          aria-label="تحرير المكون"
        >
          <Edit className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="icon"
          onClick={(e) => {
            e.stopPropagation();
            onDelete?.(item);
          }}
          className="h-8 w-8 bg-background/95 hover:bg-destructive hover:text-destructive-foreground border-border hover:border-destructive shadow-lg backdrop-blur-sm transition-all duration-200"
          title="حذف المكون"
          aria-label="حذف المكون"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>

      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <Badge className={`${getTypeColor(item.type)} transition-colors duration-200`}>
            {getTypeLabel(item.type)}
          </Badge>
        </div>
      </CardHeader>

      <CardContent>
        <div className="space-y-4">
          {/* النص مع الألوان والخط المخصص */}
          <div className="text-center">
            <div className="text-3xl font-bold arabic-text arabic-text-content" style={{ whiteSpace: 'pre-wrap' }}>
              {item.parts.map((part, index) => {
                // تحديد فئة CSS حسب وزن الخط
                const weightClass = part.fontWeight === 'light' ? 'arabic-text-light' :
                                  part.fontWeight === 'bold' ? 'arabic-text-bold' :
                                  'arabic-text-normal';

                return (
                  <span
                    key={index}
                    className={`arabic-text ${weightClass}`}
                    style={{
                      color: part.color || '#000000',
                      fontFamily: 'Q4Arabic !important',
                      fontWeight: part.fontWeight === 'light' ? '300' : part.fontWeight === 'bold' ? '700' : '400',
                      whiteSpace: 'pre-wrap', // الحفاظ على المسافات المتعددة
                    }}
                  >
                    {part.text}
                  </span>
                );
              })}
            </div>
          </div>

          {/* أزرار الصوت */}
          {(item.audioUrl || item.spellingAudioUrl) && (
            <div className="flex gap-2 justify-center">
              {/* زر الإيقاف - يظهر فقط للمكون الذي يشغل الصوت حالياً */}
              {(playingAudioId === `${item.id}-normal` || playingAudioId === `${item.id}-spelling`) && (
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2 bg-red-50 hover:bg-red-500 hover:text-white border-red-200 hover:border-red-500 text-red-600 transition-all duration-200"
                  onClick={(e) => {
                    e.stopPropagation();
                    stopAudio?.();
                  }}
                  title="إيقاف الصوت"
                  aria-label="إيقاف الصوت"
                >
                  <Volume2 className="h-4 w-4" />
                  إيقاف
                </Button>
              )}

              {item.audioUrl && (
                <Button
                  variant="outline"
                  size="sm"
                  className={`flex items-center gap-2 transition-all duration-200 ${
                    playingAudioId === `${item.id}-normal`
                      ? 'bg-green-500 text-white border-green-500'
                      : 'bg-background hover:bg-green-500 hover:text-white border-border hover:border-green-500'
                  }`}
                  onClick={(e) => {
                    e.stopPropagation();
                    playAudio?.(item.audioUrl!, 'normal', item.id);
                  }}
                  title="استماع طبيعي"
                  aria-label="استماع طبيعي"
                  disabled={playingAudioId === `${item.id}-normal`}
                >
                  <Volume2 className="h-4 w-4" />
                  استماع
                </Button>
              )}

              {item.spellingAudioUrl && (
                <Button
                  variant="outline"
                  size="sm"
                  className={`flex items-center gap-2 transition-all duration-200 ${
                    playingAudioId === `${item.id}-spelling`
                      ? 'bg-blue-500 text-white border-blue-500'
                      : 'bg-background hover:bg-blue-500 hover:text-white border-border hover:border-blue-500'
                  }`}
                  onClick={(e) => {
                    e.stopPropagation();
                    playAudio?.(item.spellingAudioUrl!, 'spelling', item.id);
                  }}
                  title="استماع تهجئة"
                  aria-label="استماع تهجئة"
                  disabled={playingAudioId === `${item.id}-spelling`}
                >
                  <Volume2 className="h-4 w-4" />
                  تهجئة
                </Button>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

// مكون البطاقة الفردية للعرض القائمة
const ListCard: React.FC<{
  item: LessonItem;
  onEdit?: (item: LessonItem) => void;
  onDelete?: (item: LessonItem) => void;
  playAudio?: (url: string, type: 'normal' | 'spelling', itemId?: string) => void;
  stopAudio?: () => void;
  playingAudioId?: string | null;
}> = ({ item, onEdit, onDelete, playAudio, stopAudio, playingAudioId }) => {
  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'letter': return 'حرف';
      case 'word': return 'كلمة';
      case 'sentence': return 'جملة';
      default: return type;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'letter': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'word': return 'bg-green-100 text-green-800 border-green-200';
      case 'sentence': return 'bg-purple-100 text-purple-800 border-purple-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <Card
      className="group transition-all duration-300 hover:shadow-lg border hover:border-primary/30"
      style={{
        backgroundColor: item.backgroundColor || undefined,
        borderColor: item.backgroundColor && item.backgroundColor !== '#ffffff' ? 'rgba(0,0,0,0.15)' : undefined
      }}
    >
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          {/* أزرار التحرير والحذف - في الجانب الأيسر */}
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="icon"
              onClick={(e) => {
                e.stopPropagation();
                onEdit?.(item);
              }}
              className="h-8 w-8 bg-background/95 hover:bg-primary hover:text-primary-foreground border-border hover:border-primary shadow-md backdrop-blur-sm transition-all duration-200"
              title="تحرير المكون"
              aria-label="تحرير المكون"
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={(e) => {
                e.stopPropagation();
                onDelete?.(item);
              }}
              className="h-8 w-8 bg-background/95 hover:bg-destructive hover:text-destructive-foreground border-border hover:border-destructive shadow-md backdrop-blur-sm transition-all duration-200"
              title="حذف المكون"
              aria-label="حذف المكون"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>

          {/* المحتوى الرئيسي */}
          <div className="flex items-center gap-4 flex-1 justify-center">
            {/* النص مع الخط المخصص */}
            <div className="text-xl font-bold arabic-text arabic-text-content">
              {item.parts.map((part, index) => {
                // تحديد فئة CSS حسب وزن الخط
                const weightClass = part.fontWeight === 'light' ? 'arabic-text-light' :
                                  part.fontWeight === 'bold' ? 'arabic-text-bold' :
                                  'arabic-text-normal';

                return (
                  <span
                    key={index}
                    className={`arabic-text ${weightClass}`}
                    style={{
                      color: part.color || '#000000',
                      fontFamily: 'Q4Arabic !important',
                      fontWeight: part.fontWeight === 'light' ? '300' : part.fontWeight === 'bold' ? '700' : '400',
                      whiteSpace: 'pre-wrap', // الحفاظ على المسافات المتعددة
                    }}
                  >
                    {part.text}
                  </span>
                );
              })}
            </div>

            {/* النوع */}
            <Badge className={getTypeColor(item.type)}>
              {getTypeLabel(item.type)}
            </Badge>
          </div>

          {/* أزرار الصوت - في الجانب الأيمن */}
          <div className="flex items-center gap-2">
            {/* زر الإيقاف - يظهر فقط للمكون الذي يشغل الصوت حالياً */}
            {(playingAudioId === `${item.id}-normal` || playingAudioId === `${item.id}-spelling`) && (
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-1 bg-red-50 hover:bg-red-500 hover:text-white border-red-200 hover:border-red-500 text-red-600 transition-all duration-200"
                onClick={(e) => {
                  e.stopPropagation();
                  stopAudio?.();
                }}
                title="إيقاف الصوت"
                aria-label="إيقاف الصوت"
              >
                <Volume2 className="h-3 w-3" />
                <span className="hidden sm:inline">إيقاف</span>
              </Button>
            )}

            {item.audioUrl && (
              <Button
                variant="outline"
                size="sm"
                className={`flex items-center gap-1 transition-all duration-200 ${
                  playingAudioId === `${item.id}-normal`
                    ? 'bg-green-500 text-white border-green-500'
                    : 'bg-background hover:bg-green-500 hover:text-white border-border hover:border-green-500'
                }`}
                onClick={(e) => {
                  e.stopPropagation();
                  playAudio?.(item.audioUrl!, 'normal', item.id);
                }}
                title="استماع طبيعي"
                aria-label="استماع طبيعي"
                disabled={playingAudioId === `${item.id}-normal`}
              >
                <Volume2 className="h-3 w-3" />
                <span className="hidden sm:inline">استماع</span>
              </Button>
            )}

            {item.spellingAudioUrl && (
              <Button
                variant="outline"
                size="sm"
                className={`flex items-center gap-1 transition-all duration-200 ${
                  playingAudioId === `${item.id}-spelling`
                    ? 'bg-blue-500 text-white border-blue-500'
                    : 'bg-background hover:bg-blue-500 hover:text-white border-border hover:border-blue-500'
                }`}
                onClick={(e) => {
                  e.stopPropagation();
                  playAudio?.(item.spellingAudioUrl!, 'spelling', item.id);
                }}
                title="استماع تهجئة"
                aria-label="استماع تهجئة"
                disabled={playingAudioId === `${item.id}-spelling`}
              >
                <Volume2 className="h-3 w-3" />
                <span className="hidden sm:inline">تهجئة</span>
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// المكون الرئيسي
export const LessonContentCards: React.FC<LessonContentCardsProps> = ({
  items,
  viewMode,
  onEdit,
  onDelete,
  playAudio,
  stopAudio,
  playingAudioId
}) => {
  // التحقق من تحميل الخط العربي المخصص
  useEffect(() => {
    FontChecker.enforceQ4ArabicOnly();

    // تسجيل معلومات الخط في وضع التطوير
    if (process.env.NODE_ENV === 'development') {
      setTimeout(() => {
        FontChecker.logFontInfo();
      }, 500);
    }
  }, []);

  if (viewMode === 'grid') {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {items.map(item => (
          <GridCard
            key={item.id}
            item={item}
            onEdit={onEdit}
            onDelete={onDelete}
            playAudio={playAudio}
            stopAudio={stopAudio}
            playingAudioId={playingAudioId}
          />
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {items.map(item => (
        <ListCard
          key={item.id}
          item={item}
          onEdit={onEdit}
          onDelete={onDelete}
          playAudio={playAudio}
          stopAudio={stopAudio}
          playingAudioId={playingAudioId}
        />
      ))}
    </div>
  );
};
