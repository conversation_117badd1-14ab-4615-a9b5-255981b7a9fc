const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function createAdminUser() {
  try {
    console.log('🚀 بدء عملية إنشاء حساب المدير...');

    // تشفير كلمة المرور
    const adminPassword = await bcrypt.hash('admin-857649', 10);
    console.log('🔐 تم تشفير كلمة المرور بنجاح');

    // إنشاء أو تحديث المستخدم المدير
    const adminUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {
        password: adminPassword,
        name: 'مدير النظام',
        updatedAt: new Date(),
      },
      create: {
        email: '<EMAIL>',
        password: adminPassword,
        name: 'مدير النظام',
        settings: {
          create: {
            preferredTheme: 'light',
            preferredLanguage: 'ar',
            emailNotifications: true,
            pushNotifications: true,
            soundEnabled: true,
          },
        },
      },
      include: {
        settings: true,
      },
    });

    console.log('✅ تم إنشاء/تحديث حساب المدير بنجاح!');
    console.log('📧 البريد الإلكتروني:', adminUser.email);
    console.log('👤 الاسم:', adminUser.name);
    console.log('🆔 معرف المستخدم:', adminUser.id);
    console.log('📅 تاريخ الإنشاء:', adminUser.createdAt);
    console.log('🔄 تاريخ التحديث:', adminUser.updatedAt);

    // التحقق من إعدادات المستخدم
    if (adminUser.settings) {
      console.log('⚙️ إعدادات المستخدم:');
      console.log('  - المظهر المفضل:', adminUser.settings.preferredTheme);
      console.log('  - اللغة المفضلة:', adminUser.settings.preferredLanguage);
      console.log('  - الإشعارات عبر البريد:', adminUser.settings.emailNotifications);
      console.log('  - الإشعارات المباشرة:', adminUser.settings.pushNotifications);
      console.log('  - الصوت مفعل:', adminUser.settings.soundEnabled);
    }

    console.log('\n🎯 بيانات تسجيل الدخول:');
    console.log('📧 البريد الإلكتروني: <EMAIL>');
    console.log('🔑 كلمة المرور: admin-857649');
    console.log('🌐 رابط تسجيل الدخول: http://localhost:3000/login');
    console.log('🏠 لوحة التحكم: http://localhost:3000/dashboard');

    return adminUser;
  } catch (error) {
    console.error('❌ حدث خطأ أثناء إنشاء حساب المدير:', error);

    if (error.code === 'P1001') {
      console.error('🔌 خطأ في الاتصال بقاعدة البيانات. تأكد من:');
      console.error('   - صحة رابط قاعدة البيانات في ملف .env');
      console.error('   - أن قاعدة البيانات متاحة ويمكن الوصول إليها');
    } else if (error.code === 'P2002') {
      console.error('📧 البريد الإلكتروني مستخدم بالفعل');
    }

    throw error;
  } finally {
    await prisma.$disconnect();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل الدالة
createAdminUser()
  .then(() => {
    console.log('\n🎉 تمت العملية بنجاح!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 فشلت العملية:', error.message);
    process.exit(1);
  });
