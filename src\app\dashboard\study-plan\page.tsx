"use client";

import React, { useState, useEffect } from "react";
import { DashboardLayout } from "@/components/dashboard/layout";
import { Loading } from "@/components/dashboard/loading";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { Calendar, Clock, BookOpen, GraduationCap, CheckCircle2, ArrowLeft } from "lucide-react";
import Link from "next/link";
import { useAuthContext } from "@/contexts/AuthContext";

interface StudyPlan {
  id: string;
  title: string;
  description: string;
  duration: string;
  level: "beginner" | "intermediate" | "advanced";
  progress: number;
  startDate: string;
  endDate: string;
  parts: StudyPlanPart[];
}

interface StudyPlanPart {
  id: string;
  title: string;
  description: string;
  progress: number;
  lessons: StudyPlanLesson[];
}

interface StudyPlanLesson {
  id: string;
  title: string;
  duration: number;
  completed: boolean;
}

export default function StudyPlanPage() {
  const { user } = useAuthContext();
  const [loading, setLoading] = useState(true);
  const [studyPlan, setStudyPlan] = useState<StudyPlan | null>(null);
  const [activeTab, setActiveTab] = useState("overview");
  
  useEffect(() => {
    const fetchStudyPlan = async () => {
      try {
        // في الواقع، هذا سيكون طلب API
        // const response = await fetch('/api/study-plan');
        // const data = await response.json();
        
        // محاكاة البيانات
        const mockStudyPlan: StudyPlan = {
          id: "1",
          title: "خطة تعلم اللغة العربية للمبتدئين",
          description: "خطة شاملة لتعلم أساسيات اللغة العربية للمبتدئين",
          duration: "3 أشهر",
          level: "beginner",
          progress: 35,
          startDate: "2023-10-01",
          endDate: "2023-12-31",
          parts: [
            {
              id: "1",
              title: "الحروف الأبجدية",
              description: "تعلم الحروف العربية وأشكالها",
              progress: 80,
              lessons: [
                {
                  id: "1",
                  title: "الحروف من أ إلى خ",
                  duration: 20,
                  completed: true
                },
                {
                  id: "2",
                  title: "الحروف من د إلى ض",
                  duration: 25,
                  completed: true
                },
                {
                  id: "3",
                  title: "الحروف من ط إلى ي",
                  duration: 20,
                  completed: false
                }
              ]
            },
            {
              id: "2",
              title: "الحركات والتشكيل",
              description: "تعلم الحركات والتشكيل في اللغة العربية",
              progress: 40,
              lessons: [
                {
                  id: "4",
                  title: "الفتحة والضمة والكسرة",
                  duration: 30,
                  completed: true
                },
                {
                  id: "5",
                  title: "السكون والشدة",
                  duration: 25,
                  completed: false
                }
              ]
            },
            {
              id: "3",
              title: "الكلمات البسيطة",
              description: "تعلم كتابة وقراءة الكلمات البسيطة",
              progress: 0,
              lessons: [
                {
                  id: "6",
                  title: "كلمات من حرفين",
                  duration: 35,
                  completed: false
                },
                {
                  id: "7",
                  title: "كلمات من ثلاثة أحرف",
                  duration: 40,
                  completed: false
                }
              ]
            }
          ]
        };
        
        // تأخير مصطنع لمحاكاة تحميل البيانات
        setTimeout(() => {
          setStudyPlan(mockStudyPlan);
          setLoading(false);
        }, 1000);
      } catch (error) {
        console.error("Error fetching study plan:", error);
        toast.error("حدث خطأ أثناء تحميل الخطة الدراسية");
        setLoading(false);
      }
    };

    fetchStudyPlan();
  }, []);
  
  // تحويل مستوى الصعوبة إلى نص عربي
  const getLevelText = (level: string) => {
    switch (level) {
      case "beginner":
        return "مبتدئ";
      case "intermediate":
        return "متوسط";
      case "advanced":
        return "متقدم";
      default:
        return level;
    }
  };
  
  // تنسيق التاريخ
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("ar-SA", {
      year: "numeric",
      month: "long",
      day: "numeric"
    });
  };
  
  // حساب الوقت المتبقي
  const calculateRemainingTime = () => {
    if (!studyPlan) return "";
    
    const endDate = new Date(studyPlan.endDate);
    const today = new Date();
    
    const diffTime = endDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) {
      return "انتهت الخطة";
    } else if (diffDays === 0) {
      return "اليوم هو آخر يوم";
    } else {
      return `${diffDays} يوم متبقي`;
    }
  };
  
  // حساب إجمالي الدروس والدروس المكتملة
  const calculateLessonsStats = () => {
    if (!studyPlan) return { total: 0, completed: 0 };
    
    let total = 0;
    let completed = 0;
    
    studyPlan.parts.forEach(part => {
      total += part.lessons.length;
      completed += part.lessons.filter(lesson => lesson.completed).length;
    });
    
    return { total, completed };
  };
  
  const lessonsStats = calculateLessonsStats();
  
  return (
    <DashboardLayout>
      <div className="mb-8">
        <h1 className="text-3xl font-bold">الخطة الدراسية</h1>
        <p className="text-muted-foreground">خطة تعلم اللغة العربية المخصصة لك</p>
      </div>
      
      {loading ? (
        <Loading text="جاري تحميل الخطة الدراسية..." />
      ) : studyPlan ? (
        <>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="overview">نظرة عامة</TabsTrigger>
              <TabsTrigger value="parts">الأجزاء</TabsTrigger>
              <TabsTrigger value="schedule">الجدول الزمني</TabsTrigger>
            </TabsList>
            
            <TabsContent value="overview">
              <div className="grid gap-6 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle>{studyPlan.title}</CardTitle>
                    <CardDescription>{studyPlan.description}</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">التقدم الإجمالي</span>
                        <span className="text-sm font-medium">{studyPlan.progress}%</span>
                      </div>
                      <Progress value={studyPlan.progress} className="h-2" />
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-1">
                        <span className="text-sm text-muted-foreground">المستوى</span>
                        <div className="flex items-center">
                          <Badge variant="outline">
                            {getLevelText(studyPlan.level)}
                          </Badge>
                        </div>
                      </div>
                      
                      <div className="space-y-1">
                        <span className="text-sm text-muted-foreground">المدة</span>
                        <div className="flex items-center">
                          <Clock className="mr-1 h-4 w-4 text-muted-foreground" />
                          <span>{studyPlan.duration}</span>
                        </div>
                      </div>
                      
                      <div className="space-y-1">
                        <span className="text-sm text-muted-foreground">تاريخ البداية</span>
                        <div className="flex items-center">
                          <Calendar className="mr-1 h-4 w-4 text-muted-foreground" />
                          <span>{formatDate(studyPlan.startDate)}</span>
                        </div>
                      </div>
                      
                      <div className="space-y-1">
                        <span className="text-sm text-muted-foreground">تاريخ الانتهاء</span>
                        <div className="flex items-center">
                          <Calendar className="mr-1 h-4 w-4 text-muted-foreground" />
                          <span>{formatDate(studyPlan.endDate)}</span>
                        </div>
                      </div>
                    </div>
                    
                    <Separator />
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-1">
                        <span className="text-sm text-muted-foreground">الوقت المتبقي</span>
                        <div className="text-lg font-medium">{calculateRemainingTime()}</div>
                      </div>
                      
                      <div className="space-y-1">
                        <span className="text-sm text-muted-foreground">الدروس المكتملة</span>
                        <div className="text-lg font-medium">
                          {lessonsStats.completed} / {lessonsStats.total}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle>الدروس القادمة</CardTitle>
                    <CardDescription>الدروس التالية في خطتك الدراسية</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {studyPlan.parts.flatMap(part => 
                        part.lessons.filter(lesson => !lesson.completed)
                      ).slice(0, 3).map(lesson => (
                        <div key={lesson.id} className="flex items-center justify-between rounded-lg border p-4">
                          <div className="space-y-1">
                            <div className="font-medium">{lesson.title}</div>
                            <div className="flex items-center text-sm text-muted-foreground">
                              <Clock className="mr-1 h-4 w-4" />
                              {lesson.duration} دقيقة
                            </div>
                          </div>
                          <Link href={`/dashboard/lessons/${lesson.id}`}>
                            <Button size="sm">
                              ابدأ الآن
                              <ArrowLeft className="ml-2 h-4 w-4" />
                            </Button>
                          </Link>
                        </div>
                      ))}
                      
                      {studyPlan.parts.flatMap(part => 
                        part.lessons.filter(lesson => !lesson.completed)
                      ).length === 0 && (
                        <div className="flex flex-col items-center justify-center py-6">
                          <CheckCircle2 className="mb-2 h-12 w-12 text-green-500" />
                          <h3 className="text-lg font-medium">أكملت جميع الدروس!</h3>
                          <p className="text-muted-foreground">
                            لقد أكملت جميع الدروس في خطتك الدراسية
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
            
            <TabsContent value="parts">
              <div className="space-y-6">
                {studyPlan.parts.map((part, index) => (
                  <Card key={part.id}>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div>
                          <CardTitle>الجزء {index + 1}: {part.title}</CardTitle>
                          <CardDescription>{part.description}</CardDescription>
                        </div>
                        <div className="text-2xl font-bold">{part.progress}%</div>
                      </div>
                      <Progress value={part.progress} className="h-2" />
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {part.lessons.map(lesson => (
                          <div key={lesson.id} className="flex items-center justify-between rounded-lg border p-4">
                            <div className="flex items-center space-x-4 space-x-reverse">
                              <div className={`flex h-10 w-10 items-center justify-center rounded-full ${
                                lesson.completed ? "bg-green-100" : "bg-primary/10"
                              }`}>
                                {lesson.completed ? (
                                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                                ) : (
                                  <BookOpen className="h-5 w-5 text-primary" />
                                )}
                              </div>
                              <div className="space-y-1">
                                <div className="font-medium">{lesson.title}</div>
                                <div className="flex items-center text-sm text-muted-foreground">
                                  <Clock className="mr-1 h-4 w-4" />
                                  {lesson.duration} دقيقة
                                </div>
                              </div>
                            </div>
                            <Link href={`/dashboard/lessons/${lesson.id}`}>
                              <Button variant={lesson.completed ? "outline" : "default"} size="sm">
                                {lesson.completed ? "مراجعة" : "ابدأ الآن"}
                                <ArrowLeft className="ml-2 h-4 w-4" />
                              </Button>
                            </Link>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
            
            <TabsContent value="schedule">
              <Card>
                <CardHeader>
                  <CardTitle>الجدول الزمني</CardTitle>
                  <CardDescription>الجدول الزمني المقترح لإكمال خطتك الدراسية</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-lg font-medium">المدة الإجمالية</h3>
                        <p className="text-muted-foreground">{studyPlan.duration}</p>
                      </div>
                      <div>
                        <h3 className="text-lg font-medium">الوقت المتبقي</h3>
                        <p className="text-muted-foreground">{calculateRemainingTime()}</p>
                      </div>
                    </div>
                    
                    <Separator />
                    
                    <div>
                      <h3 className="mb-4 text-lg font-medium">الجدول المقترح</h3>
                      <div className="space-y-4">
                        <div className="rounded-lg border p-4">
                          <h4 className="font-medium">الأسبوع 1-2</h4>
                          <p className="text-muted-foreground">الوحدة 1: الحروف الأبجدية</p>
                          <div className="mt-2 text-sm">
                            <p>• الدرس 1: الحروف من أ إلى خ</p>
                            <p>• الدرس 2: الحروف من د إلى ض</p>
                            <p>• الدرس 3: الحروف من ط إلى ي</p>
                          </div>
                        </div>
                        
                        <div className="rounded-lg border p-4">
                          <h4 className="font-medium">الأسبوع 3-4</h4>
                          <p className="text-muted-foreground">الوحدة 2: الحركات والتشكيل</p>
                          <div className="mt-2 text-sm">
                            <p>• الدرس 4: الفتحة والضمة والكسرة</p>
                            <p>• الدرس 5: السكون والشدة</p>
                          </div>
                        </div>
                        
                        <div className="rounded-lg border p-4">
                          <h4 className="font-medium">الأسبوع 5-6</h4>
                          <p className="text-muted-foreground">الوحدة 3: الكلمات البسيطة</p>
                          <div className="mt-2 text-sm">
                            <p>• الدرس 6: كلمات من حرفين</p>
                            <p>• الدرس 7: كلمات من ثلاثة أحرف</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <Separator />
                    
                    <div>
                      <h3 className="mb-2 text-lg font-medium">نصائح للدراسة</h3>
                      <ul className="list-inside list-disc space-y-2 text-muted-foreground">
                        <li>خصص وقتاً ثابتاً للدراسة كل يوم، حتى لو كان قصيراً</li>
                        <li>استمع إلى الأصوات وكرر النطق بصوت عالٍ</li>
                        <li>تدرب على الكتابة يومياً</li>
                        <li>استخدم البطاقات التعليمية لحفظ المفردات</li>
                        <li>حاول استخدام ما تعلمته في محادثات بسيطة</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </>
      ) : (
        <div className="flex flex-col items-center justify-center py-12">
          <GraduationCap className="mb-4 h-12 w-12 text-muted-foreground" />
          <h2 className="text-xl font-medium">لا توجد خطة دراسية</h2>
          <p className="text-muted-foreground">
            لم يتم العثور على خطة دراسية مخصصة لك
          </p>
          <Button asChild className="mt-4">
            <Link href="/dashboard/lessons">استعرض الدروس</Link>
          </Button>
        </div>
      )}
    </DashboardLayout>
  );
}
