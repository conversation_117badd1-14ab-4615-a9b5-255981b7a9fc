"use client";

import React, { useState, useEffect } from "react";
import { DashboardLayout } from "@/components/dashboard/layout";
import { Loading } from "@/components/dashboard/loading";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import { Search, BookOpen, Pencil, Trash2, FileText } from "lucide-react";

// المكونات المساعدة
import { LessonsList } from "@/components/dashboard/admin/lessons-list";
import { AddPartDialog } from "@/components/dashboard/admin/add-part-dialog";
import { AddLessonDialog } from "@/components/dashboard/admin/add-lesson-dialog";
import { ConfirmDialog } from "@/components/dashboard/dialogs/confirm-dialog";

// الأنواع
interface Part {
  id: string;
  title: string;
  description: string;
  order: number;
  lessonsCount: number;
}

// تم إزالة واجهة Unit - لم تعد مطلوبة

interface Lesson {
  id: string;
  title: string;
  description: string;
  partId: string;
  partTitle?: string;
  content: string;
  order: number;
  level: "beginner" | "intermediate" | "advanced";
  duration: number;
}

export default function AdminLessonsPage() {
  // حالة التحميل
  const [loading, setLoading] = useState(true);

  // بيانات الأجزاء والدروس
  const [parts, setParts] = useState<Part[]>([]);
  const [lessons, setLessons] = useState<Lesson[]>([]);

  // البحث
  const [searchQuery, setSearchQuery] = useState("");

  // حالة الحوارات
  const [isAddPartDialogOpen, setIsAddPartDialogOpen] = useState(false);
  const [isAddLessonDialogOpen, setIsAddLessonDialogOpen] = useState(false);
  const [isDeletePartDialogOpen, setIsDeletePartDialogOpen] = useState(false);
  const [isDeleteLessonDialogOpen, setIsDeleteLessonDialogOpen] = useState(false);

  // العناصر المحددة
  const [selectedPart, setSelectedPart] = useState<Part | null>(null);
  const [selectedLesson, setSelectedLesson] = useState<Lesson | null>(null);

  // جلب البيانات من API الحقيقي
  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('🔄 بدء جلب البيانات من APIs...');

        const [partsResponse, lessonsResponse] = await Promise.all([
          fetch('/api/parts'),
          fetch('/api/lessons')
        ]);

        // التحقق من نجاح جميع الطلبات
        if (!partsResponse.ok) {
          throw new Error(`خطأ في جلب الأجزاء: ${partsResponse.status}`);
        }
        if (!lessonsResponse.ok) {
          throw new Error(`خطأ في جلب الدروس: ${lessonsResponse.status}`);
        }

        const [partsData, lessonsData] = await Promise.all([
          partsResponse.json(),
          lessonsResponse.json()
        ]);

        console.log('✅ تم جلب البيانات:', {
          parts: partsData.parts?.length || 0,
          lessons: lessonsData.lessons?.length || 0
        });

        setParts(partsData.parts || []);
        setLessons(lessonsData.lessons || []);
        setLoading(false);

      } catch (error: any) {
        console.error("❌ خطأ في جلب البيانات:", error);
        toast.error(`حدث خطأ أثناء تحميل البيانات: ${error.message}`);
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // إضافة دالة لإعادة جلب البيانات
  const refreshData = async () => {
    setLoading(true);
    const fetchData = async () => {
      try {
        const [partsResponse, lessonsResponse] = await Promise.all([
          fetch('/api/parts'),
          fetch('/api/lessons')
        ]);

        if (partsResponse.ok && lessonsResponse.ok) {
          const [partsData, lessonsData] = await Promise.all([
            partsResponse.json(),
            lessonsResponse.json()
          ]);

          setParts(partsData.parts || []);
          setLessons(lessonsData.lessons || []);
        }
      } catch (error) {
        console.error('Error refreshing data:', error);
      } finally {
        setLoading(false);
      }
    };
    await fetchData();
  };



  // تصفية الأجزاء والدروس حسب البحث
  const filteredParts = parts.filter(part =>
    part.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    part.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredLessons = lessons.filter(lesson =>
    lesson.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    lesson.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (lesson.partTitle && lesson.partTitle.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // إضافة جزء جديد
  const handleAddPart = async (part: Omit<Part, "id" | "lessonsCount">) => {
    try {
      toast.loading("جاري إضافة الجزء...", { id: 'add-part' });

      const response = await fetch('/api/parts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(part),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل في إضافة الجزء');
      }

      const result = await response.json();
      setParts([...parts, result.part]);
      toast.success("تم إضافة الجزء بنجاح", { id: 'add-part' });
      setIsAddPartDialogOpen(false);

    } catch (error: any) {
      console.error('Error adding part:', error);
      toast.error(`حدث خطأ أثناء إضافة الجزء: ${error.message}`, { id: 'add-part' });
    }
  };

  // تم إزالة دالة إضافة الوحدة - لم تعد مطلوبة

  // إضافة درس جديد
  const handleAddLesson = async (lesson: Omit<Lesson, "id" | "partTitle">) => {
    try {
      toast.loading("جاري إضافة الدرس...", { id: 'add-lesson' });

      const response = await fetch('/api/lessons', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(lesson),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل في إضافة الدرس');
      }

      const result = await response.json();
      setLessons([...lessons, result.lesson]);
      toast.success("تم إضافة الدرس بنجاح", { id: 'add-lesson' });
      setIsAddLessonDialogOpen(false);

      // إعادة جلب البيانات لتحديث العدادات
      await refreshData();

    } catch (error: any) {
      console.error('Error adding lesson:', error);
      toast.error(`حدث خطأ أثناء إضافة الدرس: ${error.message}`, { id: 'add-lesson' });
    }
  };

  // حذف جزء
  const handleDeletePart = async () => {
    if (!selectedPart) return;

    try {
      toast.loading("جاري حذف الجزء...", { id: 'delete-part' });

      const response = await fetch(`/api/parts/${selectedPart.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل في حذف الجزء');
      }

      toast.success("تم حذف الجزء بنجاح", { id: 'delete-part' });
      setIsDeletePartDialogOpen(false);
      setSelectedPart(null);

      // إعادة جلب البيانات لتحديث القوائم
      await refreshData();

    } catch (error: any) {
      console.error('Error deleting part:', error);
      toast.error(`حدث خطأ أثناء حذف الجزء: ${error.message}`, { id: 'delete-part' });
    }
  };

  // تم إزالة دالة حذف الوحدة - لم تعد مطلوبة

  // حذف درس
  const handleDeleteLesson = async () => {
    if (!selectedLesson) return;

    try {
      toast.loading("جاري حذف الدرس...", { id: 'delete-lesson' });

      const response = await fetch(`/api/lessons/${selectedLesson.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل في حذف الدرس');
      }

      toast.success("تم حذف الدرس وجميع مكوناته بنجاح", { id: 'delete-lesson' });
      setIsDeleteLessonDialogOpen(false);
      setSelectedLesson(null);

      // إعادة جلب البيانات لتحديث القوائم
      await refreshData();

    } catch (error: any) {
      console.error('Error deleting lesson:', error);
      toast.error(`حدث خطأ أثناء حذف الدرس: ${error.message}`, { id: 'delete-lesson' });
    }
  };

  return (
    <DashboardLayout>
      <div className="mb-8">
        <h1 className="text-3xl font-bold">إدارة الأجزاء والدروس</h1>
        <p className="text-muted-foreground">إدارة المحتوى التعليمي بالتسلسل الهرمي: الأجزاء → الدروس → المكونات</p>
      </div>

      {loading ? (
        <Loading text="جاري تحميل البيانات..." />
      ) : (
        <>
          <div className="mb-6 flex flex-col justify-between gap-4 md:flex-row md:items-center">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="البحث في الأجزاء والدروس..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            <div className="flex gap-2 flex-wrap">
              <Button onClick={() => setIsAddPartDialogOpen(true)}>
                <BookOpen className="mr-2 h-4 w-4" />
                إضافة جزء
              </Button>
              <Button onClick={() => setIsAddLessonDialogOpen(true)}>
                <FileText className="mr-2 h-4 w-4" />
                إضافة درس
              </Button>
            </div>
          </div>

          <Tabs defaultValue="parts" className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="parts">الأجزاء</TabsTrigger>
              <TabsTrigger value="lessons">الدروس</TabsTrigger>
            </TabsList>

            <TabsContent value="parts">
              <Card>
                <CardHeader>
                  <CardTitle>الأجزاء التعليمية</CardTitle>
                  <CardDescription>قائمة بجميع الأجزاء التعليمية في النظام (المستوى الأعلى)</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {filteredParts.length === 0 ? (
                      <div className="text-center py-8 text-muted-foreground">
                        لا توجد أجزاء متاحة
                      </div>
                    ) : (
                      filteredParts.map((part) => (
                        <div key={part.id} className="border rounded-lg p-4 hover:bg-muted/50 transition-colors">
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <h3 className="font-semibold text-lg">{part.title}</h3>
                              <p className="text-muted-foreground text-sm mt-1">{part.description}</p>
                              <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                                <span>الترتيب: {part.order}</span>
                                <span>عدد الدروس: {part.lessonsCount}</span>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  setSelectedPart(part);
                                  // هنا يمكن فتح حوار التعديل
                                }}
                              >
                                <Pencil className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  setSelectedPart(part);
                                  setIsDeletePartDialogOpen(true);
                                }}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* تم إزالة تبويب الوحدات - لم تعد مطلوبة */}

            <TabsContent value="lessons">
              <Card>
                <CardHeader>
                  <CardTitle>الدروس التعليمية</CardTitle>
                  <CardDescription>قائمة بجميع الدروس التعليمية في النظام</CardDescription>
                </CardHeader>
                <CardContent>
                  <LessonsList
                    lessons={filteredLessons}
                    onEdit={(lesson) => {
                      setSelectedLesson(lesson);
                      // هنا يمكن فتح حوار التعديل
                    }}
                    onDelete={(lesson) => {
                      setSelectedLesson(lesson);
                      setIsDeleteLessonDialogOpen(true);
                    }}
                  />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* حوارات الإضافة والحذف */}
          <AddPartDialog
            open={isAddPartDialogOpen}
            onOpenChange={setIsAddPartDialogOpen}
            onSubmit={handleAddPart}
          />

          {/* تم إزالة حوار إضافة الوحدة - لم تعد مطلوبة */}

          <AddLessonDialog
            open={isAddLessonDialogOpen}
            onOpenChange={setIsAddLessonDialogOpen}
            parts={parts}
            onSubmit={handleAddLesson}
          />

          <ConfirmDialog
            open={isDeletePartDialogOpen}
            onOpenChange={setIsDeletePartDialogOpen}
            title="حذف الجزء"
            description={`هل أنت متأكد من حذف الجزء "${selectedPart?.title}"؟ لا يمكن التراجع عن هذا الإجراء.`}
            confirmText="حذف"
            cancelText="إلغاء"
            onConfirm={handleDeletePart}
            variant="destructive"
          />

          {/* تم إزالة حوار حذف الوحدة - لم تعد مطلوبة */}

          <ConfirmDialog
            open={isDeleteLessonDialogOpen}
            onOpenChange={setIsDeleteLessonDialogOpen}
            title="حذف الدرس"
            description={`هل أنت متأكد من حذف الدرس "${selectedLesson?.title}"؟ لا يمكن التراجع عن هذا الإجراء.`}
            confirmText="حذف"
            cancelText="إلغاء"
            onConfirm={handleDeleteLesson}
            variant="destructive"
          />
        </>
      )}
    </DashboardLayout>
  );
}
