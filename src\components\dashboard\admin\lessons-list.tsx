"use client";

import React from "react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>l, Trash2, FileText, Clock } from "lucide-react";
import Link from "next/link";
import { ResponsiveTable, ResponsiveTableRow, ResponsiveTableCell, ResponsiveTableActionsCell } from "@/components/ui/responsive-table";

interface Lesson {
  id: string;
  title: string;
  description: string;
  partId: string;
  partTitle?: string;
  content: string;
  order: number;
  level: "beginner" | "intermediate" | "advanced";
  duration: number;
}

interface LessonsListProps {
  lessons: Lesson[];
  onEdit: (lesson: Lesson) => void;
  onDelete: (lesson: Lesson) => void;
}

export function LessonsList({ lessons, onEdit, onDelete }: LessonsListProps) {
  // تحويل مستوى الصعوبة إلى نص عربي
  const getLevelText = (level: string) => {
    switch (level) {
      case "beginner":
        return "مبتدئ";
      case "intermediate":
        return "متوسط";
      case "advanced":
        return "متقدم";
      default:
        return level;
    }
  };

  // تحويل مستوى الصعوبة إلى لون
  const getLevelColor = (level: string) => {
    switch (level) {
      case "beginner":
        return "bg-green-100 text-green-800";
      case "intermediate":
        return "bg-blue-100 text-blue-800";
      case "advanced":
        return "bg-purple-100 text-purple-800";
      default:
        return "";
    }
  };

  const headers = ["الجزء", "العنوان", "المستوى", "المدة", "الإجراءات"];

  return (
    <div className="rounded-md border">
      {/* جدول للشاشات الكبيرة */}
      <div className="hidden md:block">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>الجزء</TableHead>
              <TableHead>العنوان</TableHead>
              <TableHead>المستوى</TableHead>
              <TableHead>المدة</TableHead>
              <TableHead className="text-left">الإجراءات</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {lessons.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="h-24 text-center">
                  لا توجد دروس. قم بإضافة درس جديد.
                </TableCell>
              </TableRow>
            ) : (
              lessons.map((lesson) => (
                <TableRow key={lesson.id}>
                  <TableCell>
                    <div className="flex flex-col">
                      <span>{lesson.partTitle || "غير محدد"}</span>
                      <span className="text-xs text-muted-foreground">الترتيب: {lesson.order}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-col">
                      <span className="font-medium">{lesson.title}</span>
                      <span className="text-xs text-muted-foreground truncate max-w-xs">{lesson.description}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={getLevelColor(lesson.level)} variant="outline">
                      {getLevelText(lesson.level)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Clock className="mr-1 h-3 w-3 text-muted-foreground" />
                      <span>{lesson.duration} دقيقة</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Link href={`/dashboard/admin/lessons/${lesson.id}`}>
                        <Button variant="ghost" size="icon">
                          <FileText className="h-4 w-4" />
                        </Button>
                      </Link>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => onEdit(lesson)}
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => onDelete(lesson)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* جدول متجاوب للشاشات الصغيرة */}
      <div className="md:hidden">
        <ResponsiveTable headers={headers}>
          {lessons.length === 0 ? (
            <ResponsiveTableRow>
              <ResponsiveTableCell className="h-24 text-center">
                لا توجد دروس. قم بإضافة درس جديد.
              </ResponsiveTableCell>
              <ResponsiveTableCell className="hidden md:table-cell">-</ResponsiveTableCell>
              <ResponsiveTableCell className="hidden md:table-cell">-</ResponsiveTableCell>
              <ResponsiveTableCell className="hidden md:table-cell">-</ResponsiveTableCell>
              <ResponsiveTableCell className="hidden md:table-cell">-</ResponsiveTableCell>
            </ResponsiveTableRow>
          ) : (
            lessons.map((lesson) => (
              <ResponsiveTableRow key={lesson.id}>
                <ResponsiveTableCell header="الجزء">
                  <div className="flex flex-col">
                    <span>{lesson.partTitle || "غير محدد"}</span>
                    <span className="text-xs text-muted-foreground">الترتيب: {lesson.order}</span>
                  </div>
                </ResponsiveTableCell>
                <ResponsiveTableCell header="العنوان">
                  <div className="flex flex-col">
                    <span className="font-medium">{lesson.title}</span>
                    <span className="text-xs text-muted-foreground">{lesson.description}</span>
                  </div>
                </ResponsiveTableCell>
                <ResponsiveTableCell header="المستوى">
                  <Badge className={getLevelColor(lesson.level)} variant="outline">
                    {getLevelText(lesson.level)}
                  </Badge>
                </ResponsiveTableCell>
                <ResponsiveTableCell header="المدة">
                  <div className="flex items-center">
                    <Clock className="mr-1 h-3 w-3 text-muted-foreground" />
                    <span>{lesson.duration} دقيقة</span>
                  </div>
                </ResponsiveTableCell>
                <ResponsiveTableActionsCell>
                  <Link href={`/dashboard/admin/lessons/${lesson.id}`}>
                    <Button variant="ghost" size="icon">
                      <FileText className="h-4 w-4" />
                    </Button>
                  </Link>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => onEdit(lesson)}
                  >
                    <Pencil className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => onDelete(lesson)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </ResponsiveTableActionsCell>
              </ResponsiveTableRow>
            ))
          )}
        </ResponsiveTable>
      </div>
    </div>
  );
}
